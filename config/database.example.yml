development:
    adapter: mysql2
    database: sharetribe_development
    encoding: utf8mb4
    collation: utf8mb4_unicode_ci
    username: vagrant
    password:
    host: localhost

# Warning: The database defined as "test" will be erased and
# re-generated from your development database when you run "rake".
# Do not set this db to the same as development or production.
test: &test
    adapter: mysql2
    database: sharetribe_test
    encoding: utf8mb4
    collation: utf8mb4_unicode_ci
    username: vagrant
    password:
    host: localhost

staging:
    adapter: mysql2
    database: sharetribe_staging
    encoding: utf8mb4
    collation: utf8mb4_unicode_ci
    username: vagrant
    password:
    host: localhost

production:
    adapter: mysql2
    database: sharetribe_production
    encoding: utf8mb4
    collation: utf8mb4_unicode_ci
    username: vagrant
    password:
    host: localhost

cucumber:
  <<: *test
