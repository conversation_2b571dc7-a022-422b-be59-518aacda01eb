# In Rails 5+, ActiveRecord::ConnectionAdapters::ConnectionManagement was removed
# Rails 7 uses ActiveRecord::ConnectionHandling instead

# Create a middleware that clears connections after each request
class ConnectionManagementMiddleware
  def initialize(app)
    @app = app
  end

  def call(env)
    response = @app.call(env)
    # Rails 7.2 changed the method name from clear_active_connections! to clear_active_connections
    if ActiveRecord::Base.respond_to?(:clear_active_connections)
      ActiveRecord::Base.clear_active_connections
    elsif ActiveRecord::Base.respond_to?(:clear_active_connections!)
      ActiveRecord::Base.clear_active_connections!
    end
    response
  end
end

# Add the middleware to the Rails application
Rails.application.config.middleware.use ConnectionManagementMiddleware

# For backward compatibility
module ActiveRecord
  module ConnectionAdapters
    module ConnectionManagement
      def self.included(base)
        # No-op for compatibility
      end
    end
  end
end
