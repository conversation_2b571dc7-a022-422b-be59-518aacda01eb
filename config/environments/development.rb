require_relative './common.rb'

Rails.application.configure do
  # Set eager_load to false for development
  config.eager_load = false
  APP_CONFIG ||= ConfigLoader.load_app_config

  # Settings specified here will take precedence over those in config/environment.rb

  # In the development environment your application's code is reloaded on
  # every request.  This slows down response time but is perfect for development
  # since you don't have to restart the webserver when you make code changes.
  config.cache_classes = false

  # Allow all hosts in development
  config.hosts.clear

  # Chargebee credentials
  config.chargebee_public_key = 'test_Sti3063OKgcu2pNsf11fSIrhCleIOhV7W'
  config.chargebee_api_key = 'test_clfwvCsRo95JFKRdvpmBzEVthU0GLchP'

  config.chargebee_site_name = 'petmasters-test'
  # config.chargebee_api_key = 'live_FcudzIxlk7VcuX1hGVo43LvRC5f91EWCip'
  # config.chargebee_site_name = 'petmasters'

  #OpenTok Keys
  config.opentok_api_key = '46747892'
  config.opentok_secret_key = 'eb12e00c9384bc7834db6c7e866cca8d3bce655d'

  # To autoload MailPreview, uncomment this line
  # (this is a hack which is fixed properly in Rails 4)
  # config.action_view.cache_template_loading = false

  # Basic log config, for calls to Rails.logger.<level> { <message> }
  config.logger = ::Logger.new(STDOUT)
  # Formats log entries into: LEVEL MESSAGE
  # Heroku adds to this timestamp and worker/dyno id, so datetime can be stripped
  config.logger.formatter = ->(severity, datetime, progname, msg) { "#{severity} #{msg}\n" }
  config.logger = ActiveSupport::TaggedLogging.new(config.logger)

  # Lograge config, overrides default instrumentation for logging ActionController and ActionView logging
  config.lograge.enabled = true
  config.lograge.custom_options = ->(event) {
    params = event.payload[:params].except('controller', 'action')

    { params: params,
      host: event.payload[:host],
      community_id: event.payload[:community_id],
      current_user_id: event.payload[:current_user_id],
      user_agent: event.payload[:user_agent],
      referer: event.payload[:referer],
      forwarded_for: event.payload[:forwarded_for],
      request_uuid: event.payload[:request_uuid] }
  }

  config.lograge.formatter = Lograge::Formatters::Json.new

  config.after_initialize do
    ActiveRecord::Base.logger = Rails.logger.clone
    ActiveRecord::Base.logger.level = Logger::DEBUG
    ActionMailer::Base.logger = Rails.logger.clone
    ActionMailer::Base.logger.level = Logger::INFO
  end



  # Show full error reports and disable caching
  config.consider_all_requests_local       = true

  # Enable/disable caching. By default caching is disabled.
  # Run rails dev:cache to toggle caching.
  if Rails.root.join('tmp/caching-dev.txt').exist?
    config.action_controller.perform_caching = true
    config.action_controller.enable_fragment_cache_logging = true
    config.cache_store = :memory_store, { :namespace => "sharetribe-dev"}
    config.public_file_server.headers = {
      'Cache-Control' => "public, max-age=#{2.days.to_i}"
    }
  else
    config.action_controller.perform_caching = false
    config.cache_store = :null_store
  end

  config.action_controller.action_on_unpermitted_parameters = :raise

  # Don't care if the mailer can't send
  config.action_mailer.raise_delivery_errors = true

  config.action_mailer.perform_caching = false

  config.action_mailer.default_url_options = { port: 3000 }

  config.action_mailer.asset_host = 'http://localhost:3000'

  # Print deprecation notices to the Rails logger.
  config.active_support.deprecation = :log

  # Raise an error on page load if there are pending migrations.
  config.active_record.migration_error = :page_load

  # Expands the lines which load the assets
  config.assets.debug = false
  config.assets.quiet = true

  # CDN url for assets
  config.cdn_asset_url = "https://assets.petworks.com"

  # Raises error for missing translations


  config.action_mailer.delivery_method = :smtp
  config.action_mailer.smtp_settings = { :address => "localhost", :port => 1025 }

  # Use an evented file watcher to asynchronously detect changes in source code,
  # routes, locales, etc. This feature depends on the listen gem.
  if ENV['SUPPRESS_EVENTED_FILE_UPDATE_CHECKER']
    config.file_watcher = ActiveSupport::FileUpdateChecker
  else
    config.file_watcher = ActiveSupport::EventedFileUpdateChecker
  end
end
