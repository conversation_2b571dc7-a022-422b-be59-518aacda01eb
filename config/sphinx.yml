development:
  address: <%= ENV['SPHINX_HOST'] || 'localhost' %>
  port: <%= ENV['SPHINX_PORT'] || 9312 %>
  bin_path: "/usr/bin"
  searchd_binary_name: searchd
  indexer_binary_name: indexer
  enable_star: true
  min_infix_len: 1
  mem_limit: 256M
  version: 3.7.0

test:
  address: localhost
  port: 9312
  bin_path: "/usr/bin"
  searchd_binary_name: searchd
  indexer_binary_name: indexer
  enable_star: true
  min_infix_len: 1
  mem_limit: 256M
  version: 3.7.0

staging:
  address: <%= ENV['SPHINX_HOST'] || 'localhost' %>
  port: <%= ENV['SPHINX_PORT'] || 9312 %>
  bin_path: "/usr/bin"
  searchd_binary_name: searchd
  indexer_binary_name: indexer
  enable_star: true
  min_infix_len: 1
  mem_limit: 256M
  version: 3.7.0

production:
  address: <%= ENV['SPHINX_HOST'] || 'localhost' %>
  port: <%= ENV['SPHINX_PORT'] || 9312 %>
  bin_path: "/usr/bin"
  searchd_binary_name: searchd
  indexer_binary_name: indexer
  enable_star: true
  min_infix_len: 1
  mem_limit: 256M
  version: 3.7.0
