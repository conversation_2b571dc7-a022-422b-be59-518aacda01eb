# encoding: utf-8
# == Schema Information
#
# Table name: listings
#
#  id                              :integer          not null, primary key
#  uuid                            :binary(16)       not null
#  community_id                    :integer          not null
#  author_id                       :string(255)
#  category_old                    :string(255)
#  title                           :string(255)
#  times_viewed                    :integer          default(0)
#  language                        :string(255)
#  created_at                      :datetime
#  updates_email_at                :datetime
#  updated_at                      :datetime
#  last_modified                   :datetime
#  sort_date                       :datetime
#  listing_type_old                :string(255)
#  description                     :text(65535)
#  origin                          :string(255)
#  destination                     :string(255)
#  valid_until                     :datetime
#  delta                           :boolean          default(TRUE), not null
#  open                            :boolean          default(TRUE)
#  share_type_old                  :string(255)
#  privacy                         :string(255)      default("private")
#  comments_count                  :integer          default(0)
#  subcategory_old                 :string(255)
#  old_category_id                 :integer
#  category_id                     :integer
#  share_type_id                   :integer
#  listing_shape_id                :integer
#  transaction_process_id          :integer
#  shape_name_tr_key               :string(255)
#  action_button_tr_key            :string(255)
#  price_cents                     :integer
#  currency                        :string(255)
#  quantity                        :string(255)
#  unit_type                       :string(32)
#  quantity_selector               :string(32)
#  unit_tr_key                     :string(64)
#  unit_selector_tr_key            :string(64)
#  deleted                         :boolean          default(FALSE)
#  require_shipping_address        :boolean          default(FALSE)
#  pickup_enabled                  :boolean          default(FALSE)
#  shipping_price_cents            :integer
#  shipping_price_additional_cents :integer
#  availability                    :string(32)       default("none")
#  url_slug                        :string(255)
#  author_name                     :string(255)
#  featured_priority               :integer          default(0), not null
#  pm_approved                     :string(255)
#  pm_approved_url                 :string(255)
#  youtube_url                     :string(255)
#  service_area_distance           :string(255)
#  booking_percentile              :float(24)
#  booknow_optout                  :boolean          default(FALSE), not null
#
# Indexes
#
#  homepage_query                      (community_id,open,sort_date,deleted)
#  homepage_query_valid_until          (community_id,open,valid_until,sort_date,deleted)
#  index_listings_on_category_id       (old_category_id)
#  index_listings_on_community_id      (community_id)
#  index_listings_on_listing_shape_id  (listing_shape_id)
#  index_listings_on_new_category_id   (category_id)
#  index_listings_on_open              (open)
#  index_listings_on_uuid              (uuid) UNIQUE
#  person_listings                     (community_id,author_id)
#  updates_email_listings              (community_id,open,updates_email_at)
#

class Listing < ApplicationRecord

  include ApplicationHelper
  include ActionView::Helpers::TranslationHelper
  include Rails.application.routes.url_helpers

  belongs_to :author, :class_name => "Person", :foreign_key => "author_id"

  has_many :listing_images, -> { where("error IS NULL").order("position") }, :dependent => :destroy

  has_many :conversations
  has_many :comments, :dependent => :destroy
  has_many :custom_field_values, :dependent => :destroy
  has_many :custom_dropdown_field_values, :class_name => "CustomFieldValues::DropdownFieldValue"
  has_many :custom_checkbox_field_values, :class_name => "CustomFieldValues::CheckboxFieldValue"
  has_many :social_media
  accepts_nested_attributes_for :social_media

  has_many :listing_awards
  has_many :awards, :through => :listing_awards
  has_many :pricing_packages
  has_many :custom_quotes

  has_one :location, :dependent => :destroy
  has_one :origin_loc, -> { where('location_type = ?', 'origin_loc') }, :class_name => "Location", :dependent => :destroy
  has_one :destination_loc, -> { where('location_type = ?', 'destination_loc') }, :class_name => "Location", :dependent => :destroy
  accepts_nested_attributes_for :origin_loc, :destination_loc

  has_and_belongs_to_many :followers, :class_name => "Person", :join_table => "listing_followers"

  belongs_to :category
  has_and_belongs_to_many :additional_categories, class_name: 'Category', join_table: :listings_additional_categories, association_foreign_key: :additional_category_id

  monetize :price_cents, :allow_nil => true, with_model_currency: :currency
  monetize :shipping_price_cents, allow_nil: true, with_model_currency: :currency
  monetize :shipping_price_additional_cents, allow_nil: true, with_model_currency: :currency

  before_validation :set_valid_until_time
  before_save :add_city_state_to_title
  before_save :set_url_slug

  before_save :set_author_name

  validates_presence_of :author_id
  
  before_create :set_sort_date_to_now

  #scope for listings with pet_pros without subscription
  scope :no_subscription, -> {
  joins(:author).where.not(
    author_id: Person.joins(:chargebee_subscriptions)
                      .where('chargebee_subscriptions.status = ? OR chargebee_subscriptions.id IS NULL', 'active')
                      .pluck(:person_id)
  )
  } 

  #scope for listings with pet_pros with subscription
  scope :with_subscription, -> { joins(:author).where(author_id: Person.joins(:chargebee_subscriptions).pluck(:person_id)) }

  def authors_confirmed_transactions
    Transaction.where(current_state: ["auto_confirmed", "confirmed", "pet_pro_confirmed", "paid"], listing_id: self.id)
  end

  def recent_transactions(from: 3.months.ago, to: Time.now)
    Transaction.where(current_state: ["auto_confirmed", "confirmed", "pet_pro_confirmed", "paid"], listing_id: self.id, created_at: from..to)
  end

  def recent_transactions_total(from: 3.months.ago, to: Time.now)
    transactions = recent_transactions(from: from, to: to)
    transactions.inject(0) { |sum, t| sum + (t.total || 0) }
  end

  def calculate_booking_percentile
    category_total = self.category.total_for_booking_percentile
    recent_transactions_total = self.recent_transactions_total
    
    if recent_transactions_total > 0 && category_total > 0
        # Calculate percentile based on amount of money made vs category total
        booking_percentile = ((recent_transactions_total / category_total) * 100).round(2)
        # Update listing
        begin
            self.update_attribute(:booking_percentile, booking_percentile)
            logger.info("SUCCESS::Calculate Listing Booking Percentile - listing_id: #{self.id} - booking_percentile: #{self.booking_percentile} - category_id: #{self.category_id} - category_total: #{category_total} - recent_transactions_total: #{recent_transactions_total}")
        rescue => e
            logger.error("ERROR::Calculate Listing Booking Percentile - listing_id: #{self.id} - booking_percentile: #{self.booking_percentile} - category_id: #{self.category_id} - category_total: #{category_total} - recent_transactions_total: #{recent_transactions_total}")
            logger.error("ERROR::Calculate Listing Booking Percentile - #{e.message}")
        end   
    end
  end

  def set_sort_date_to_now
    self.sort_date ||= Time.now
  end

  before_create :set_updates_email_at_to_now
  def set_updates_email_at_to_now
    self.updates_email_at ||= Time.now
  end

  def add_city_state_to_title
    
    city_comma_state = self.city_state_code
  
    # First, remove any occurrences of " - Nationwide"
    self.title = self.title.gsub(" - Nationwide", "")
  
    # Then, remove the city and state
    self.title = self.title.gsub(" - " + city_comma_state, "")
    
    # Then if its a national category with no service radius specified, append ' - National' to the listing title
    if self.category.national? && (self.service_area_distance.blank? || self.service_area_distance == '0')
      self.title = self.title + " - Nationwide"
    # Otherwise append the city and state
    elsif !city_comma_state.blank? && self.title.exclude?(city_comma_state)
      self.title = self.title + " - " + city_comma_state
    end 
  end
  

  def city_state_code
    origin_split = String(origin).split(",")
    if origin_split.length > 2
      "#{origin_split[-3].strip}, #{origin_split[-2].tr("0-9 ", "")}"
    else
      ''
    end
  end

  def uuid_object
    if self[:uuid].nil?
      nil
    else
      UUIDUtils.parse_raw(self[:uuid])
    end
  end

  def uuid_object=(uuid)
    self.uuid = UUIDUtils.raw(uuid)
  end

  before_create :add_uuid
  def add_uuid
    self.uuid ||= UUIDUtils.create_raw
  end

  before_validation do
    # Normalize browser line-breaks.
    # Reason: Some browsers send line-break as \r\n which counts for 2 characters making the
    # 5000 character max length validation to fail.
    # This could be more general helper function, if this is needed in other textareas.
    self.description = description.gsub("\r\n","\n") if self.description
  end
  validates_length_of :description, :maximum => 5000, :allow_nil => true
  validates_presence_of :category
  validates_inclusion_of :valid_until, :allow_nil => :true, :in => DateTime.now..DateTime.now + 7.months
  validates_numericality_of :price_cents, :only_integer => true, :greater_than_or_equal_to => 0, :message => "price must be numeric", :allow_nil => true

  def self.currently_open(status="open")
    status = "open" if status.blank?
    case status
    when "all"
      where([])
    when "open"
      where(["open = '1' AND (valid_until IS NULL OR valid_until > ?)", DateTime.now])
    when "closed"
      where(["open = '0' OR (valid_until IS NOT NULL AND valid_until < ?)", DateTime.now])
    end
  end

  def visible_to?(current_user, current_community)
    # DEPRECATED
    #
    # Consider removing the `visible_to?` method.
    #
    # Reason: Authorization logic should be in the controller layer (filters etc.),
    # not in the model layer.
    #
    ListingVisibilityGuard.new(self, current_community, current_user).visible?
  end

  # sets the time to midnight
  def set_valid_until_time
    if valid_until
      self.valid_until = valid_until.utc + (23-valid_until.hour).hours + (59-valid_until.min).minutes + (59-valid_until.sec).seconds
    end
  end

  # Overrides the to_param method to implement clean URLs
  def to_param
    self.class.to_param(id, title)
  end

  def self.to_param(id, title)
    "#{id}-#{title.to_url}"
  end

  def clean_url
    url_slug
  end

  def set_url_slug
    self.url_slug = Listing.to_url_slug(self)
  end

  def set_author_name
    self.author_name = self.author.public_name unless self.author.nil?
  end

  def self.to_url_slug(listing)
    url_slug = listing.title.gsub(/[^0-9a-z\- ]/i, '').to_url
    existing_listing = Listing.find_by_url_slug(url_slug)
    return url_slug if !existing_listing || existing_listing == listing


    count = 2
    while Listing.where(url_slug: "#{url_slug}-#{count}").count > 0 do
      count += 1
    end

    "#{url_slug}-#{count}"
  end

  def self.find_by_url(url)
    results = Listing.where(url_slug: url)
    results.length == 1 ? results.first : Listing.find(url)
  end

  def self.find_by_category_and_subcategory(category)
    Listing.where(:category_id => category.own_and_subcategory_ids)
  end

  # Returns true if listing exists and valid_until is set
  def temporary?
    !new_record? && valid_until
  end

  def update_fields(params)
    update_attribute(:valid_until, nil) unless params[:valid_until]
    params[:last_modified] = Time.now.strftime("%d/%m/%Y %H:%M")
    update_attributes(params)
  end

  def closed?
    !open? || (valid_until && valid_until < DateTime.now)
  end

  # Send notifications to the users following this listing
  # when the listing is updated (update=true) or a
  # new comment to the listing is created.
  def notify_followers(community, current_user, update)
    followers.each do |follower|
      unless follower.id == current_user.id
        if update
          MailCarrier.deliver_now(PersonMailer.new_update_to_followed_listing_notification(self, follower, community))
        else
          MailCarrier.deliver_now(PersonMailer.new_comment_to_followed_listing_notification(comments.last, follower, community))
        end
      end
    end
  end

  def image_by_id(id)
    listing_images.find_by_id(id)
  end

  def prev_and_next_image_ids_by_id(id)
    listing_image_ids = listing_images.collect(&:id)
    ArrayUtils.next_and_prev(listing_image_ids, id);
  end

  def has_image?
    !listing_images.empty?
  end

  def icon_name
    category.icon_name
  end

  # The price symbol based on this listing's price or community default, if no price set
  def price_symbol
    price ? price.symbol : MoneyRails.default_currency.symbol
  end

  def answer_for(custom_field)
    custom_field_values.find { |value| value.custom_field_id == custom_field.id }
  end

  def unit_type
    Maybe(read_attribute(:unit_type)).to_sym.or_else(nil)
  end

  def init_origin_location(location)
    if location.present?
      build_origin_loc(location.attributes)
    else
      build_origin_loc()
    end
  end

  def ensure_origin_loc
    build_origin_loc unless origin_loc
  end

  def custom_field_value_factory(custom_field_id, answer_value)
    question = CustomField.find(custom_field_id)

    answer = question.with_type do |question_type|
      case question_type
      when :dropdown
        option_id = answer_value.to_i
        answer = CustomFieldValues::DropdownFieldValue.new
        answer.custom_field_option_selections = [CustomFieldOptionSelection.new(:custom_field_value => answer,
                                                                                :custom_field_option_id => option_id,
                                                                                :listing_id => self.id)]
        answer
      when :text
        answer = CustomFieldValues::TextFieldValue.new
        answer.text_value = answer_value
        answer
      when :numeric
        answer = CustomFieldValues::NumericFieldValue.new
        answer.numeric_value = ParamsService.parse_float(answer_value)
        answer
      when :checkbox
        answer = CustomFieldValues::CheckboxFieldValue.new
        answer.custom_field_option_selections = answer_value.map { |value|
          CustomFieldOptionSelection.new(:custom_field_value => answer, :custom_field_option_id => value, :listing_id => self.id)
        }
        answer
      when :date_field
        answer = CustomFieldValues::DateFieldValue.new
        answer.date_value = Time.utc(answer_value["(1i)"].to_i,
                                     answer_value["(2i)"].to_i,
                                     answer_value["(3i)"].to_i)
        answer
      else
        raise ArgumentError.new("Unimplemented custom field answer for question #{question_type}")
      end
    end

    answer.question = question
    answer.listing_id = self.id
    return answer
  end

  # Note! Requires that parent self is already saved to DB. We
  # don't use association to link to self but directly connect to
  # self_id.
  def upsert_field_values!(custom_field_params)
    custom_field_params ||= {}

    # Delete all existing
    custom_field_value_ids = self.custom_field_values.map(&:id)
    CustomFieldOptionSelection.where(custom_field_value_id: custom_field_value_ids).delete_all
    CustomFieldValue.where(id: custom_field_value_ids).delete_all

    field_values = custom_field_params.map do |custom_field_id, answer_value|
      custom_field_value_factory(custom_field_id, answer_value) unless is_answer_value_blank(answer_value)
    end.compact

    # Insert new custom fields in a single transaction
    CustomFieldValue.transaction do
      field_values.each(&:save!)
    end
  end

  def is_answer_value_blank(value)
    if value.is_a?(Hash)
      value["(3i)"].blank? || value["(2i)"].blank? || value["(1i)"].blank?  # DateFieldValue check
    else
      value.blank?
    end
  end

  def reorder_listing_images(params, user_id)
    listing_image_ids =
      if params[:listing_images]
        params[:listing_images].collect { |h| h[:id] }.select { |id| id.present? }
      else
        logger.error("Listing images array is missing", nil, {params: params})
        []
      end

    ListingImage.where(id: listing_image_ids, author_id: user_id).update_all(listing_id: self.id)

    if params[:listing_ordered_images].present?
      params[:listing_ordered_images].split(",").each_with_index do |image_id, position|
        ListingImage.where(id: image_id, author_id: user_id).update_all(position: position+1)
      end
    end
  end

  def logger
    @logger ||= SharetribeLogger.new(:listing, logger_metadata.keys).tap { |logger|
      logger.add_metadata(logger_metadata)
    }
  end

  def logger_metadata
    { listing_id: id }
  end

  def animals
    self.custom_checkbox_field_values.detect{|q| q.question.name.downcase == 'animals'}.selected_options.map {|o| o.title}
  end

  def has_pricing_packages
    return false if self.pricing_packages.nil?
    
    active_pricing_packages = self.pricing_packages.where(deleted: false)
    
    active_pricing_packages.count > 0 && self.author.payments_enabled?
  end
  

  def service_area_distance_options
    {
      "Any Distance" => 0,
      "5 miles" => 5,
      "10 miles" => 10,
      "25 miles" => 25,
      "50 miles" => 50,
      "75 miles" => 75,
      "100 miles" => 100,
      "150 miles" => 150,
      "200 miles" => 200,
      "250 miles" => 250,
      "500 miles" => 500,
      "1000 miles" => 1000, 
    }
  end
end
