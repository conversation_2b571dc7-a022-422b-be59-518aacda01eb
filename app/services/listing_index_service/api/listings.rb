module ListingIndexService::Api

  RELATED_RESOURCES = [:listing_images, :author, :num_of_reviews, :location].to_set

  ListingIndexResult = ListingIndexService::DataTypes::ListingIndexResult

  class Listings

    def initialize(logger_target)
      @logger_target = logger_target
    end

    def search(community_id:, search:, includes: [], engine: :sphinx, raise_errors: false)
      unless includes.to_set <= RELATED_RESOURCES
        return Result::Error.new("Unknown included resources: #{(includes.to_set - RELATED_RESOURCES).to_a}")
      end

      add_national_featured = search[:add_national_featured]
      search[:add_national_featured] = false

      search_result = search_engine(engine, raise_errors).search(
        community_id: community_id,
        search: ListingIndexService::DataTypes.create_search_params(search),
        includes: includes
      )

      search[:add_national_featured] = add_national_featured
      if add_national_featured
        search.merge!({add_national_featured: true})
        search_result = concat_out_of_state_featured(search, engine, includes, community_id, raise_errors, search_result)
      end

      search_result.maybe().map { |res|
        Result::Success.new(
          process_results(res, engine)
        )
      }.or_else {
        raise search_result.data if raise_errors
        log_error(search_result, community_id)
        search_result
      }
    end

    private

    #takes in information for a DataTypes search and Result and returns a modified version of the original result
    #uses the search page to calculate the results with an offset based on result of previous Result
    def concat_out_of_state_featured(search, engine, includes, community_id, raise_errors, original_result)
        original_count = original_result.data[:count] < 1 ? 1 : original_result.data[:count]
        original_per_page = search[:per_page]
        offset = original_count % search[:per_page]
        original_last_page = (original_count / search[:per_page].to_f).ceil
        new_search_page = (original_last_page >= search[:page]) ? 1 : search[:page] + 1 - original_last_page
        new_search_per_page = (new_search_page * search[:per_page]) - offset

        search[:page] = 1
        search[:per_page] = new_search_per_page
        featured_search = search_engine(engine, raise_errors).search(
          community_id: community_id,
          search: ListingIndexService::DataTypes.create_search_params(search),
          includes: includes
        )
        original_result.data[:count] = original_count + featured_search.data[:count]

        featured_search_listings = featured_search.data[:listings]
        first_listings = featured_search_listings.shift(original_per_page - offset) 
        grouped_listings = featured_search_listings.in_groups_of(original_per_page, false)
        #account for when offset is 0
        grouped_listings.unshift(first_listings) unless first_listings.empty?
        if original_result.data[:listings].length < original_per_page
          original_result.data[:listings] = original_result.data[:listings].concat(grouped_listings[new_search_page -1]) unless grouped_listings[new_search_page -1].nil?
        end
        return original_result
    end

    def process_results(results, engine)
      # Commenting out code to filter out listings who have non responsive authors
      filtered_listings = results[:listings]#.reject { |listing| listing[:author][:is_non_responder] }

      ListingIndexResult.call(
        count: results[:count],
        listings: filtered_listings.map { |search_res|
          search_res.merge(url: "#{search_res[:id]}-#{search_res[:title].to_url}")
        }
      )
    end

    def search_engine(engine, raise_errors)
      case engine
      when :sphinx
        ListingIndexService::Search::SphinxAdapter.new
      when :zappy
        ListingIndexService::Search::ZappyAdapter.new(raise_errors: raise_errors)
      else
        raise NotImplementedError.new("Adapter for search engine #{engine} not implemented")
      end
    end

    def log_error(err_response, community_id)
      logger = SharetribeLogger.new(:listing_index_service,
                                    [:marketplace_id],
                                    @logger_target)
      logger.add_metadata({marketplace_id: community_id})
      logger.error(err_response.error_msg)
    end
  end

end
