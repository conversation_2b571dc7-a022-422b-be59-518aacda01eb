require 'set'

module Petmasters::Search
  include PetTypesHelper
  
  QUERY_DISTANCE_DEFAULT = 1_000.0
  QUERY_DISTANCE_LVL_2 = 2_000.0
  QUERY_DISTANCE_LVL_3 = 3_000.0

  def create_search_data(search_result, params_service, city, state_code, params_category_id)
    if !params_service.nil? || params[:category_id]
      #get Category id from params_service
      category = params_service ? Category.find_by(url: params_service) : {id: params_category_id.to_i}
      #create location_string using city and and state_code
      if city
        #factor in that if there is no city state_code should be the name of the state
        location_string = "#{city[:name].titleize}, #{state_code.upcase}"
      elsif state_code
        location_string = "#{state_code.upcase}" #change later
      else
        location_string = nil
      end
      #map over search_results to create an array containing all the information for creating PetProSearchDatum
      search_datas = search_result[:data].map do |datum|
        {category_id: category ? category[:id] : nil,
          person_id: datum[:author][:id],
          listing_id: datum[:id],
          location_string: location_string}
      end
        # remove the call to this job for the time being while we re-evaluate (mjc 10/17/23)
        # CreateSearchDataJob.perform_later(search_datas)
    end
  end

  def find_category_for_search_term(term)
    Category
      .joins(:search_term_category_mappings)
      .where('LOWER(search_term_category_mappings.search_term) = TRIM(LOWER(?))', term)
      .take
  end

  def find_location_based_search_term(term)
    location_based_search_term =
      LocationBasedSearchTerm
      .where('search_term = ?', LocationBasedSearchTerm.transform_search_term(term))
      .take
  end

  # Adapted from HomepageController#index
  def make_search(distance: nil, per_page: 20, location: nil, state_code: nil, force_location_use: false, sort: nil, not_national: false, add_national_featured: false, city_code: nil)

    params = unsafe_params_hash.select{|k, v| v.present? }

    all_shapes = @current_community.shapes
    shape_name_map = all_shapes.map { |s| [s[:id], s[:name]]}.to_h

    filter_params = {}

    m_selected_category = Maybe(@current_community.categories.find_by_url_or_id(params[:service]))
    filter_params[:categories] = m_selected_category.own_and_subcategory_ids.or_nil
    selected_category = m_selected_category.or_nil
    relevant_filters = select_relevant_filters((m_selected_category.own_and_subcategory_ids.or_nil))
    show_price_filter = @current_community.show_price_filter && all_shapes.any? { |s| s[:price_enabled] }
    @show_custom_fields = relevant_filters.present? || show_price_filter

    listing_shape_param = params[:transaction_type]
    selected_shape = all_shapes.find { |s| s[:name] == listing_shape_param }
    filter_params[:listing_shape] = Maybe(selected_shape)[:id].or_else(nil)
    compact_filter_params = HashUtils.compact(filter_params)

    includes = [:author, :listing_images, :num_of_reviews, :location]

    main_search = search_mode
    enabled_search_modes = search_modes_in_use(params[:q], params[:lc], main_search)
    keyword_in_use = enabled_search_modes[:keyword]

    current_page = Maybe(params)[:page].to_i.map { |n| n > 0 ? n : 1 }.or_else(1)
    relevant_search_fields = parse_relevant_search_fields(params, relevant_filters)
    search_result = listings_search(params: params,
                                  current_page: current_page,
                                  listings_per_page: per_page,
                                  filter_params: compact_filter_params,
                                  includes: includes.to_set,
                                  keyword_search_in_use: keyword_in_use,
                                  relevant_search_fields: relevant_search_fields,
                                  city: force_location_use && !location.nil? ? nil :  @city,
                                  city_code: force_location_use && !location.nil? ? nil :  city_code,
                                  state_code: force_location_use && !location.nil? ? nil :  state_code,
                                  location: @city.nil? || force_location_use ? location : nil,
                                  distance: distance || @query_distance,
                                  sort: sort,
                                  not_national: not_national,
                                  add_national_featured: add_national_featured)

    locals = {
      shapes: all_shapes,
      filters: relevant_filters,
      show_price_filter: show_price_filter,
      selected_category: selected_category,
      selected_shape: selected_shape,
      shape_name_map: shape_name_map,
      main_search: main_search,
      current_page: current_page,
      current_search_path_without_page: search_path(params.except(:page)),
      search_params: CustomFieldSearchParams.remove_irrelevant_search_params(params, relevant_search_fields),
    }

    create_search_data(search_result, params[:service], @city, params[:state], params[:category_id])

    perform_search(search_result, locals)
  end

  def perform_search(search_result, locals)
    if request.xhr? || params[:is_ajax] # checks if AJAX request
      search_result.on_success { |listings|
        set_featured_items(listings)
        render partial: 'petmasters/search/parts/page', locals: { listings: listings, featured: @featured_items }
      }.on_error {
        render body: nil, status: 500
      }
    else
      search_result.on_success { |listings|
        @listings = listings
        set_featured_items(listings)

        if listings.count > 0 || @query_distance.nil?
          render locals: locals.merge(
                 seo_pagination_links: seo_pagination_links(params, @listings.current_page, @listings.total_pages))
        else
          make_search(distance: next_query_distance)
        end
      }.on_error { |e|
        flash[:error] = t("homepage.errors.search_engine_not_responding")
        @listings = Listing.none.paginate(:per_page => 1, :page => 1)
        render status: 500,
               locals: locals.merge(
                 seo_pagination_links: seo_pagination_links(params, @listings.current_page, @listings.total_pages))
      }
    end
  end

  def next_query_distance
    @query_distance = {
      QUERY_DISTANCE_DEFAULT => QUERY_DISTANCE_LVL_2,
      QUERY_DISTANCE_LVL_2 => QUERY_DISTANCE_LVL_3,
    }[@query_distance]
    return @query_distance
  end

  # Adapted from HomepageController#find_listings
  def listings_search(params:, current_page:, listings_per_page:, filter_params:, includes:, keyword_search_in_use:, relevant_search_fields:, city:, city_code:, state_code:, distance:, location:, sort:, not_national:, add_national_featured:)
  
    search = {
      # Add listing_id
      categories: filter_params[:categories],
      listing_shape_ids: Array(filter_params[:listing_shape]),
      price_cents: filter_range(params[:price_min], params[:price_max]),
      keywords: keyword_search_in_use ? params[:q] : nil,
      fields: relevant_search_fields,
      per_page: listings_per_page,
      page: current_page,
      price_min: params[:price_min],
      price_max: params[:price_max],
      locale: I18n.locale,
      include_closed: false,
      sort: nil
    }

    unless city.nil?
      search.merge!(search_filters_for_city(city, distance))
    end

    unless city_code.nil?
      search.merge!(city_code: city_code)
    end
    
   unless state_code.nil?
      search.merge!(state_code: state_code)
    end

    unless location.nil?
      search.merge!(search_filters_for_location(location, distance))
    end

    unless sort.nil?
      search.merge!({ sort: sort })
    end

    unless not_national.nil?
      search.merge!({not_national: not_national})
    end

    unless add_national_featured.nil?
      search.merge!({add_national_featured: add_national_featured})
    end

    raise_errors = Rails.env.development?

    ListingIndexService::Api::Api.listings.search(
      community_id: @current_community.id,
      search: search,
      includes: includes,
      engine: :zappy, # FeatureFlagHelper.search_engine,
      raise_errors: raise_errors
      ).and_then { |res|
      Result::Success.new(
        ListingIndexViewUtils.to_struct(
          result: res,
          includes: includes,
          page: search[:page],
          per_page: search[:per_page]
        )
      )
    }

  end

  def search_filters_for_location(location, distance = nil)
    lat = LocationUtils.to_radians(location[:latitude])
    lng = LocationUtils.to_radians(location[:longitude])

    {
      sort: :distance,
      distance_max: distance || QUERY_DISTANCE_DEFAULT,
      distance_unit: :miles,
      latitude: lat,
      longitude: lng,
    }
  end

  def search_filters_for_city(city, distance)
    return {} if city.nil? || city[:lat].nil? || city[:long].nil?
    search_filters_for_location({ latitude: city[:lat], longitude: city[:long] }, distance)
  end

  def get_simple_search_results(category: nil, limit: 20, location: nil, city: nil, state_code: nil, query: nil, distance: 100_000, categories: nil, requires_image: false, sort: nil, fields: nil)
    category_ids = categories.nil? ? Maybe(category).own_and_subcategory_ids.or_nil : categories.flat_map do |cat|
      cat.own_and_subcategory_ids
    end

    current_page = Maybe(params)[:page].to_i.map { |n| n > 0 ? n : 1 }.or_else(1)

    search = {
      categories: category_ids,
      keywords: query,
      requires_image: requires_image,
      per_page: limit,
      page: current_page,
      locale: I18n.locale,
      include_closed: false,
      sort: nil
    }

    unless city.nil?
      search.merge!(search_filters_for_city(city, distance))
    end

    unless state_code.nil?
      search.merge!(state_code: state_code)
    end

    unless location.nil?
      search.merge!(search_filters_for_location(location, distance))
    end

    unless sort.nil?
      search.merge!({ sort: sort })
    end

    unless fields.nil?
      search.merge!({ fields: fields })
    end

    raise_errors = Rails.env.development?

    ListingIndexService::Api::Api.listings.search(
      community_id: @current_community.id,
      search: search,
      includes: [:author, :listing_images, :num_of_reviews, :location],
      engine: :zappy, # FeatureFlagHelper.search_engine,
      raise_errors: raise_errors
      ).and_then { |res|
      Result::Success.new(
        ListingIndexViewUtils.to_struct(
          result: res,
          includes: [:author, :listing_images, :num_of_reviews, :location],
          page: search[:page],
          per_page: search[:per_page]
        )
      )
    }
  end

  def make_simple_search_without_render(category: nil, limit: 20, location: nil, city: nil, state_code: nil, query: nil, distance: 100_000, categories: nil, requires_image: false, sort: nil, fields: nil)
    search_result = get_simple_search_results(category: category, limit: limit, location: location, city: city, state_code: state_code, query: query, distance: distance, categories: categories, requires_image: requires_image, sort: sort, fields: fields)
    search_result.on_success { |listings|
      set_featured_items(listings)
      return { listings: listings, featured: @featured_items }
    }.on_error {
      return 'error'
    }
  end

  def make_simple_search(category: nil, limit: 20, location: nil, city: nil, state_code: nil, query: nil, distance: 100_000, categories: nil, requires_image: false, sort: nil, fields: nil)
    search_result = get_simple_search_results(category: category, limit: limit, location: location, city: city, state_code: state_code, query: query, distance: distance, categories: categories, requires_image: requires_image, sort: sort, fields: fields)
    create_search_data(search_result, params[:service], @city, params[:state], params[:category_id])
    if request.xhr? || params[:is_ajax] # checks if AJAX request
      search_result.on_success { |listings|
        set_featured_items(listings)
        render partial: 'petmasters/search/parts/page', locals: { listings: listings, featured: @featured_items }
      }.on_error {
        render body: nil, status: 500
      }
    else
      search_result.on_success { |listings|
        @simple_search_listings = listings
        set_featured_items(listings)
      }.on_error { |e|
        flash[:error] = t("homepage.errors.search_engine_not_responding")
        @simple_search_listings = Listing.none.paginate(:per_page => 1, :page => 1)
        render status: 500
      }
    end
  end

  def raw_search(category: nil, limit: 20, location: nil, city: nil, state_code: nil, query: nil, distance: 100_000, categories: nil, requires_image: false, sort: nil, fields: nil)
    category_ids = categories.nil? ? Maybe(category).own_and_subcategory_ids.or_nil : categories.flat_map do |cat|
      cat.own_and_subcategory_ids
    end


    search = {
      categories: category_ids,
      keywords: query,
      requires_image: requires_image,
      per_page: limit,
      page: 1,
      locale: I18n.locale,
      include_closed: false,
      sort: nil
    }
    
    community_id = Maybe(@community)[:id].or_else(Community.find_by_ident("petworks").id)

    unless city.nil?
      search.merge!(search_filters_for_city(city, distance))
    end

    unless state_code.nil?
      search.merge!(state_code: state_code)
    end

    unless location.nil?
      search.merge!(search_filters_for_location(location, distance))
    end

    unless sort.nil?
      search.merge!({ sort: sort })
    end

    unless fields.nil?
      search.merge!({ fields: fields })
    end

    raise_errors = Rails.env.development?

    ListingIndexService::Api::Api.listings.search(
      community_id: community_id,
      search: search,
      includes: [:author, :listing_images, :num_of_reviews, :location],
      engine: :zappy, # FeatureFlagHelper.search_engine,
      raise_errors: raise_errors
      ).and_then { |res|
      Result::Success.new(
        ListingIndexViewUtils.to_struct(
          result: res,
          includes: [:author, :listing_images, :num_of_reviews, :location],
          page: search[:page],
          per_page: search[:per_page]
        )
      )
    }
  end

  def set_featured_items(listings)
    @featured_items = {}
    correct_order = {
			licensed: "Licensed", certified: "Certified", bonded: "Bonded", insured: "Insured",
			house_calls: "House Calls", bbb_accredited: "BBB Accredited", background_checked: "Background Checked", do_you_offer_online_sessions?: "Online Sessions"
		}

    listings = Listing.includes(
      custom_checkbox_field_values: [question: [:names, :options], selected_options: :titles],
      custom_dropdown_field_values: [question: [:names, :options], selected_options: :titles]
    ).where(id: listings.map(&:id))

    listings.each do |listing|
      featuredArr = []
      featuredSet = Set.new

  		listing.custom_checkbox_field_values.each do |value|
  			question = value.question
  			selected = value.selected_options.map {|o| o.title}

  			if question.featured
  				if question.options.length == 1
            featuredSet.add(question.name.downcase.gsub(" ", "_"))
  				elsif question.options.length <= 3
            selected.each {|item| featuredSet.add(item.downcase.gsub(" ", "_"))}
  				end
  			end
  		end

  		listing.custom_dropdown_field_values.each do |value|
  			question = value.question
  			selected = value.selected_options.map {|o| o.title}

  			unless selected.empty?
  				if question.featured
  					if selected.first.downcase == "yes"
              featuredSet.add(question.name.downcase.gsub(" ", "_"))
  					elsif selected.first.downcase != "no"
              selected.each {|item| featuredSet.add(item.downcase.gsub(" ", "_"))}
  					end
  				end
  			end
  		end

      correct_order.each do |k, v|
        if featuredSet.include?(k.to_s)
          featuredArr << v
        end
      end

      @featured_items[listing.id] = featuredArr
    end
	end

  def categories_by_service_type(service_name)
    Category.where(pm_main_category_type: service_name).where.not(:url => ["pet-euthanasia", "pet-cremation", "pet-grief-counselors", "pet-burial"])
  end

  def get_nearest_suggestion_for_service_type(service_name, location, pet_type_id: nil)
    categories = categories_by_service_type(service_name)

    fields = nil
    if !pet_type_id.nil?
      fields = [ { id: PET_TYPE_CATEGORY_ID,
                value: [ pet_type_id ],
                type: :selection_group,
                operator: :and } ]
    end  
    
    rando_category = Petmasters::Categories.get(Community.find_by_ident("petworks"), categories.sample.url)
    search_result = raw_search(category: rando_category, limit: 1, location: location, fields: fields)
    search_result.on_success { |listing|
      return {listing: listing.first, category: rando_category.display_name(I18n.locale)}
    }.on_error {
      nil
    }
  end

  def get_suggestion(category, location, pet_type_id: nil)
    results = { listing: nil, category: category}
    # Retry logic in case no suggestions are returned for a given random category
    3.times do
      break if !results[:listing].nil?
      results = get_nearest_suggestion_for_service_type(category, location, pet_type_id: pet_type_id)
      logger.info("GET-SUGGESTIONS::No suggestions returned for #{category}")
    end
    results
  end

  def get_nearest_suggestions(location_info, pet_type_id: nil)
    if !location_info.nil? 
      location = { longitude: location_info.longitude, latitude: location_info.latitude }
    end
    
    wellness_suggestion = get_suggestion('wellness', location, pet_type_id: pet_type_id)
    specialty_suggestion = get_suggestion('specialty', location, pet_type_id: pet_type_id)
    time_of_need_suggestion = get_suggestion('time-of-need', location, pet_type_id: pet_type_id)

    {wellness_suggestion: wellness_suggestion, specialty_suggestion: specialty_suggestion, time_of_need_suggestion: time_of_need_suggestion}
  end
end
