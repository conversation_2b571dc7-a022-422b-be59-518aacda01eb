class PeopleController < Devise::RegistrationsController
  include ActionView::Helpers::TextHelper

  include PeopleHelper

  class PersonDeleted < StandardError; end
  class PersonBanned < StandardError; end

  skip_before_action :verify_authenticity_token, :only => [:creates]
  skip_before_action :require_no_authentication, :only => [:new]

  before_action EnsureCanAccessPerson.new(
    :id, error_message_key: "layouts.notifications.you_are_not_authorized_to_view_this_content"), only: [:update, :destroy]

  LOOSER_ACCESS_CONTROL = [
    :check_email_availability,
    :check_email_availability_and_validity,
    :check_invitation_code
  ]

  skip_before_action :cannot_access_if_banned,            only: LOOSER_ACCESS_CONTROL
  skip_before_action :cannot_access_without_confirmation, only: LOOSER_ACCESS_CONTROL
  skip_before_action :ensure_consent_given,               only: LOOSER_ACCESS_CONTROL
  skip_before_action :ensure_user_belongs_to_community,   only: LOOSER_ACCESS_CONTROL

  helper_method :show_closed?

  def show
    @person = Person.find_by!(username: params[:username], community_id: @current_community.id)
    raise PersonDeleted if @person.deleted?
    raise PersonBanned if @person.banned?

    redirect_to landing_page_path and return if @current_community.private? && !@current_user
    @selected_tribe_navi_tab = "members"
    @community_membership = CommunityMembership.find_by_person_id_and_community_id_and_status(@person.id, @current_community.id, "accepted")

    include_closed =  params[:show_closed]
    search = {
      author_id: @person.id,
      include_closed: include_closed,
      page: 1,
      per_page: [@person.listings.size, 1].max
    }

    includes = [:author, :listing_images]
    raise_errors = Rails.env.development?

    listings =
      ListingIndexService::API::Api
      .listings
      .search(
        community_id: @current_community.id,
        search: search,
        engine: FeatureFlagHelper.search_engine,
        raise_errors: raise_errors,
        includes: includes
      ).and_then { |res|
      Result::Success.new(
        ListingIndexViewUtils.to_struct(
        result: res,
        includes: includes,
        page: search[:page],
        per_page: search[:per_page]
      ))
    }.data

    received_testimonials = TestimonialViewUtils.received_testimonials_in_community(@person, @current_community)
    received_positive_testimonials = TestimonialViewUtils.received_positive_testimonials_in_community(@person, @current_community)
    feedback_positive_percentage = @person.feedback_positive_percentage_in_community(@current_community)

    # get all transactions admin approval entries for this pet parent
    transaction_admin_approvals = @person.transaction_admin_approvals

    if @person.location
      location_info = " - #{@person.location.city_state_code}"
    else
      location_info = ""
    end
    
    set_page_title("Petworks Member #{@person.public_name}#{location_info}")
    
    if listings.total_entries == 0
      add_page_head_tag "<link href='https://www.petworks.com#{person_path(@person.username, locale: nil)}' rel='canonical'> <meta name='robots' content='noindex'>"
      set_page_meta("This is the profile page of Petworks member #{@person.public_name}#{location_info}.")
    else
      add_page_head_tag "<link href='https://www.petworks.com#{person_path(@person.username, locale: nil)}' rel='canonical'>"
      set_page_meta("This is the profile page of pet professional #{@person.public_name}, a Petworks member with #{listings.total_entries} open #{listings.total_entries == 1 ? 'listing' : 'listings'}#{location_info}.")
    end    

    render locals: { listings: listings,
                     followed_people: @person.followed_people,
                     received_testimonials: received_testimonials,
                     received_positive_testimonials: received_positive_testimonials,
                     feedback_positive_percentage: feedback_positive_percentage,
                     transaction_admin_approvals: transaction_admin_approvals 
                   }
  end

  def new
    @page = MercuryPage.load(:pm_signup)
    @fields = @page.fields.symbolize_keys

    @selected_tribe_navi_tab = "members"
    redirect_to search_path if logged_in? && !@current_user.has_admin_rights?(@current_community)
    session[:invitation_code] = params[:code] if params[:code]

    @person = if params[:person] then
      Person.new(params[:person].slice(:given_name, :family_name, :email, :username, :phone_number).permit!)
    else
      Person.new()
    end

    @container_class = params[:private_community] ? "container_12" : "container_24"
    @grid_class = params[:private_community] ? "grid_6 prefix_3 suffix_3" : "grid_10 prefix_7 suffix_7"
  end

  def create
    domain = @current_community ? @current_community.full_url : "#{request.protocol}#{request.host_with_port}"
    if params[:person][:pet_pro]
      error_redirect_path = sign_up_path
    else
      error_redirect_path = join_path
    end

    if params[:person].blank? || params[:person][:input_again].present? || params[:person][:given_name] == params[:person][:family_name] # Honey pot for spammerbots
      flash[:error] = t("layouts.notifications.registration_considered_spam")
      ApplicationHelper.send_error_notification("Registration Honey Pot is hit.", "Honey pot")
      redirect_to error_redirect_path and return
    end

    if @current_community && @current_community.join_with_invite_only? || params[:invitation_code]

      unless Invitation.code_usable?(params[:invitation_code], @current_community)
        # abort user creation if invitation is not usable.
        # (This actually should not happen since the code is checked with javascript)
        session[:invitation_code] = nil # reset code from session if there was issues so that's not used again
        ApplicationHelper.send_error_notification("Invitation code check did not prevent submiting form, but was detected in the controller", "Invitation code error")

        # TODO: if this ever happens, should change the message to something else than "unknown error"
        flash[:error] = t("layouts.notifications.unknown_error")
        redirect_to error_redirect_path and return
      else
        invitation = Invitation.find_by_code(params[:invitation_code].upcase)
      end
    end

    return if email_not_valid(params, error_redirect_path)

    email = nil
    begin
      ActiveRecord::Base.transaction do
        @person, email = new_person(params, @current_community)
      end
    rescue => e
      flash[:error] = t("people.new.invalid_username_or_email")
      redirect_to error_redirect_path and return
    end

    if !params[:person][:pet_pro_reference].nil?
      reference_pro = Person.find_by(username: params[:person][:pet_pro_reference])
      PetProReferrer.create(referrer_id: reference_pro.id, new_member_id: @person.id) unless reference_pro.nil?
    end

    referer = request.referer

    if referer.include?('signup') && !referer.include?('?user')
      @person.update(pet_pro: true)
    end

    # Make person a member of the current community
    if @current_community
      membership = CommunityMembership.new(person: @person, community: @current_community, consent: @current_community.consent, signup_referer: request.referer)
      membership.status = "pending_email_confirmation"
      membership.invitation = invitation if invitation.present?
      # If the community doesn't have any members, make the first one an admin
      if @current_community.members.count == 0
        membership.admin = true
      end
      membership.save!
      session[:invitation_code] = nil
    end

    if !params[:person][:location].nil? && !params[:person][:location][:latitude].empty?
      Location.create(person_id: @person.id, location_type: "person", address: params[:person][:location][:address], google_address: params[:person][:location][:google_address], latitude: params[:person][:location][:latitude], longitude: params[:person][:location][:longitude])
    end
    # If invite was used, reduce usages left
    invitation.use_once! if invitation.present?

    Delayed::Job.enqueue(CommunityJoinedJob.new(@person.id, @current_community.id)) if @current_community

    record_event(flash, "SignUp", method: :email)
    # send email confirmation
    # (unless disabled for testing environment)
    if APP_CONFIG.skip_email_confirmation
      email.confirm!

      redirect_to search_path
    else
      if params[:person][:pet_pro]

        Email.send_confirmation(email, @current_community, 'pet_pro')

        flash[:notice] = t("layouts.notifications.account_creation_succesful_you_still_need_to_confirm_your_email")
        redirect_to pet_pro_confirmation_pending_path
      else
        Email.send_confirmation(email, @current_community)

        flash[:notice] = t("layouts.notifications.account_creation_succesful_you_still_need_to_confirm_your_email")
        redirect_to confirmation_pending_path

      end
    end
  end

  def create_facebook_based
    username = UserService::Api::Users.username_from_fb_data(
      username: session["devise.facebook_data"]["username"],
      given_name: session["devise.facebook_data"]["given_name"],
      family_name: session["devise.facebook_data"]["family_name"],
      community_id: @current_community.id)

    person_hash = {
      :username => username,
      :given_name => session["devise.facebook_data"]["given_name"],
      :family_name => session["devise.facebook_data"]["family_name"],
      :facebook_id => session["devise.facebook_data"]["id"],
      :locale => I18n.locale,
      :test_group_number => 1 + rand(4),
      :password => Devise.friendly_token[0,20],
      community_id: @current_community.id
    }

    ActiveRecord::Base.transaction do
      @person = Person.create!(person_hash)
      # We trust that Facebook has already confirmed these and save the user few clicks
      Email.create!(:address => session["devise.facebook_data"]["email"], :send_notifications => true, :person => @person, :confirmed_at => Time.now, community_id: @current_community.id)

      @person.set_default_preferences
      CommunityMembership.create(person: @person, community: @current_community, status: "pending_consent")
    end

    begin
      @person.store_picture_from_facebook!
    rescue StandardError => e
      # We can just catch and log the error, because if the profile picture upload fails
      # we still want to make the user creation pass, just without the profile picture,
      # which user can upload later
      logger.error(e.message, :facebook_new_user_profile_picture_upload_failed, { person_id: @person.id })
    end

    sign_in(resource_name, @person)
    flash[:notice] = t("layouts.notifications.login_successful", :person_name => view_context.link_to(PersonViewUtils.person_display_name_for_type(@person, "first_name_only"), person_path(@person))).html_safe


    session[:fb_join] = "pending_analytics"

    record_event(flash, "SignUp", method: :facebook)

    redirect_to pending_consent_path
  end

  def update
    target_user = Person.find_by!(username: params[:id], community_id: @current_community.id)
    # If setting new location, delete old one first
    if params[:person] && params[:person][:location] && (params[:person][:location][:address].empty? || params[:person][:street_address].blank?)
      params[:person].delete("location")
      if target_user.location
        target_user.location.delete
      end
    end

    #Check that people don't exploit changing email to be confirmed to join an email restricted community
    if params["request_new_email_confirmation"] && @current_community && ! @current_community.email_allowed?(params[:person][:email])
      flash[:error] = t("people.new.email_not_allowed")
      redirect_back(fallback_location: homepage_url) and return
    end

    target_user.set_emails_that_receive_notifications(params[:person][:send_notifications])

    begin
      person_params = person_update_params(params)

      Maybe(person_params)[:location].each { |loc|
        person_params[:location] = loc.merge(location_type: :person)
      }

      m_email_address = Maybe(person_params)[:email_attributes][:address]
      m_email_address.each { |new_email_address|
        send_notifications = person_params[:email_attributes][:send_notifications]
        # This only builds the emails, they will be saved when `update_attributes` is called
        target_user.emails.build(address: new_email_address, community_id: @current_community.id, send_notifications: send_notifications)
      }

      if target_user.update_attributes(person_params.except(:email_attributes))
        if params[:person][:password]
          #if password changed Devise needs a new sign in.
          sign_in target_user, :bypass => true
        end

        m_email_address.each {
          # A new email was added, send confirmation email to the latest address
          Email.send_confirmation(target_user.emails.last, @current_community)
        }

        flash[:notice] = t("layouts.notifications.person_updated_successfully")

        # Send new confirmation email, if was changing for that
        if params["request_new_email_confirmation"]
            target_user.send_confirmation_instructions(request.host_with_port, @current_community)
            flash[:notice] = t("layouts.notifications.email_confirmation_sent_to_new_address")
        end
      else
        flash[:error] = t("layouts.notifications.#{target_user.errors.first}")
      end
    rescue RestClient::RequestFailed => e
      flash[:error] = t("layouts.notifications.update_error")
    end

    #removes display name if
    if !target_user.display_name.blank? && params[:person] && params[:person][:given_name]
      target_user.update(display_name: nil)
    end

    redirect_back(fallback_location: homepage_url)
  end

  def destroy
    target_user = Person.find_by!(username: params[:id], community_id: @current_community.id)

    # has_unfinished check removed but might want to bring back some custom variation of it.
    # has_unfinished = TransactionService::Transaction.has_unfinished_transactions(target_user.id)
    # return redirect_to search_path if has_unfinished

    # Do all delete operations in transaction. Rollback if any of them fails
    ActiveRecord::Base.transaction do
      UserService::Api::Users.delete_user(target_user.id)
      MarketplaceService::Listing::Command.delete_listings(target_user.id)

      PaypalService::API::Api.accounts.delete(community_id: target_user.community_id, person_id: target_user.id)
    end

    sign_out target_user
    record_event(flash, 'user', {action: "deleted", opt_label: "by user"})
    flash[:notice] = t("layouts.notifications.account_deleted")
    target_user.chargebee_subscriptions.each{|subscription| subscription.cancel_subscription }
    redirect_to search_path
  end

  def check_username_availability
    respond_to do |format|
      format.json { render :json => Person.username_available?(params[:person][:username], @current_community.id) }
    end
  end

  def check_email_availability_and_validity
    email = params[:person][:email].to_s.downcase

    allowed_and_available = @current_community.email_allowed?(email) && Email.email_available?(email, @current_community.id)

    respond_to do |format|
      format.json { render json: allowed_and_available }
    end
  end

  # this checks that email is not already in use for anyone (including current user)
  def check_email_availability
    email = params[:person] && params[:person][:email_attributes] && params[:person][:email_attributes][:address]

    respond_to do |format|
      format.json { render json: Email.email_available?(email, @current_community.id) }
    end
  end

  def check_invitation_code
    respond_to do |format|
      format.json { render :json => Invitation.code_usable?(params[:invitation_code], @current_community) }
    end
  end

  def show_closed?
    params[:closed] && params[:closed].eql?("true")
  end

  private

  def person_update_params(params)
    params.require(:person).permit(
        :given_name,
        :family_name,
        :display_name,
        :street_address,
        :phone_number,
        :sms_notifications,
        :image,
        :description,
        { location: [:address, :google_address, :latitude, :longitude] },
        :password,
        :password2,
        { send_notifications: [] },
        { email_attributes: [:address, :send_notifications] },
        :min_days_between_community_updates,
        { preferences: [
          :email_from_admins,
          :email_about_new_messages,
          :email_about_new_comments_to_own_listing,
          :email_when_conversation_accepted,
          :email_when_conversation_rejected,
          :email_about_new_received_testimonials,
          :email_about_confirm_reminders,
          :email_about_testimonial_reminders,
          :email_about_completed_transactions,
          :email_about_new_payments,
          :email_about_new_listings_by_followed_people,
          :empty_notification
        ] }
      )
  end

  def email_not_valid(params, error_redirect_path)
    # strip trailing spaces
    params[:person][:email] = params[:person][:email].to_s.downcase.strip

    # Check that email is not taken
    unless Email.email_available?(params[:person][:email], @current_community.id)
      flash[:error] = t("people.new.email_is_in_use")
      redirect_to error_redirect_path
      return true
    end

    # Check that the email is allowed for current community
    if @current_community && ! @current_community.email_allowed?(params[:person][:email])
      flash[:error] = t("people.new.email_not_allowed")
      redirect_to error_redirect_path
      return true
    end

    false
  end

end
