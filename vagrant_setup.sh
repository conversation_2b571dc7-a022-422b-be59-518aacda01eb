#!/bin/bash

# Vagrant-specific Sharetribe Setup Script
# This script is designed to run inside the Vagrant VM

set -e

echo "🚀 Setting up Sharetribe in Vagrant VM..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

cd /vagrant

# 1. Update system
log_info "Updating system packages..."
sudo apt-get update -qq

# 2. Install essential packages
log_info "Installing essential packages..."
sudo apt-get install -y \
    curl \
    git \
    build-essential \
    libssl-dev \
    libreadline-dev \
    zlib1g-dev \
    libncurses5-dev \
    libffi-dev \
    libgdbm-dev \
    libyaml-dev \
    libsqlite3-dev \
    libreadline6-dev \
    uuid-dev \
    mysql-client \
    libmysqlclient-dev \
    imagemagick \
    libmagickwand-dev \
    nodejs \
    npm \
    sphinxsearch \
    mysql-server

# 3. Setup MySQL
log_info "Setting up MySQL..."
sudo systemctl start mysql
sudo systemctl enable mysql

# Create database and user
sudo mysql -e "CREATE DATABASE IF NOT EXISTS sharetribe_development;"
sudo mysql -e "CREATE USER IF NOT EXISTS 'sharetribe'@'localhost' IDENTIFIED BY 'secret';"
sudo mysql -e "GRANT ALL PRIVILEGES ON sharetribe_development.* TO 'sharetribe'@'localhost';"
sudo mysql -e "FLUSH PRIVILEGES;"

# 4. Install rbenv
log_info "Installing rbenv..."
if [ ! -d "$HOME/.rbenv" ]; then
    curl -fsSL https://github.com/rbenv/rbenv-installer/raw/HEAD/bin/rbenv-installer | bash
fi

# Add rbenv to PATH
export PATH="$HOME/.rbenv/bin:$PATH"
eval "$(rbenv init -)"

# Add to bashrc if not already there
if ! grep -q "rbenv init" ~/.bashrc; then
    echo 'export PATH="$HOME/.rbenv/bin:$PATH"' >> ~/.bashrc
    echo 'eval "$(rbenv init -)"' >> ~/.bashrc
fi

# 5. Install Ruby 3.2.2
log_info "Installing Ruby 3.2.2..."
rbenv install -s 3.2.2
rbenv global 3.2.2
rbenv rehash

# 6. Install Bundler
log_info "Installing Bundler..."
gem install bundler
rbenv rehash

# 7. Create database.yml
log_info "Creating database configuration..."
cat > config/database.yml << EOF
development:
  adapter: mysql2
  encoding: utf8mb4
  collation: utf8mb4_unicode_ci
  database: sharetribe_development
  username: sharetribe
  password: secret
  host: localhost
  port: 3306
  pool: 5
  timeout: 5000

test:
  adapter: mysql2
  encoding: utf8mb4
  collation: utf8mb4_unicode_ci
  database: sharetribe_test
  username: sharetribe
  password: secret
  host: localhost
  port: 3306
  pool: 5
  timeout: 5000

production:
  adapter: mysql2
  encoding: utf8mb4
  collation: utf8mb4_unicode_ci
  database: sharetribe_production
  username: sharetribe
  password: secret
  host: localhost
  port: 3306
  pool: 5
  timeout: 5000
EOF

# 8. Create config.yml
log_info "Creating application configuration..."
if [ ! -f "config/config.yml" ]; then
    cat > config/config.yml << EOF
development:
  domain: "localhost:3001"
  use_https: false
  
test:
  domain: "test.localhost"
  use_https: false

production:
  domain: "yourdomain.com"
  use_https: true
EOF
fi

# 9. Install gems
log_info "Installing Ruby gems..."
bundle install

# 10. Fix migrations
log_info "Fixing migrations..."
if [ -f "fix_migrations.rb" ]; then
    ruby fix_migrations.rb
fi

# 11. Setup database
log_info "Setting up database..."
bundle exec rake db:create
bundle exec rake db:migrate || {
    log_warning "Migrations failed, trying schema load..."
    bundle exec rake db:schema:load 2>/dev/null || log_warning "Schema load also failed"
}

# 12. Seed database
log_info "Seeding database..."
bundle exec rake db:seed || log_warning "Database seeding failed"

# 13. Create startup script
log_info "Creating startup script..."
cat > start_server.sh << 'EOF'
#!/bin/bash
cd /vagrant
export PATH="$HOME/.rbenv/bin:$PATH"
eval "$(rbenv init -)"
echo "🚀 Starting Sharetribe server..."
bundle exec rails server -b 0.0.0.0 -p 3000
EOF

chmod +x start_server.sh

# 14. Create systemd service (optional)
log_info "Creating systemd service..."
sudo tee /etc/systemd/system/sharetribe.service > /dev/null << EOF
[Unit]
Description=Sharetribe Rails Application
After=network.target mysql.service

[Service]
Type=simple
User=vagrant
WorkingDirectory=/vagrant
Environment=PATH=/home/<USER>/.rbenv/bin:/home/<USER>/.rbenv/shims:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
Environment=RAILS_ENV=development
ExecStart=/home/<USER>/.rbenv/shims/bundle exec rails server -b 0.0.0.0 -p 3000
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl daemon-reload

log_success "🎉 Vagrant setup completed!"
echo ""
echo "📋 To start the application:"
echo "1. Manual start: ./start_server.sh"
echo "2. Service start: sudo systemctl start sharetribe"
echo "3. Enable auto-start: sudo systemctl enable sharetribe"
echo ""
echo "🌐 Access the application:"
echo "- From host machine: http://localhost:3001"
echo "- From VM: http://localhost:3000"
echo ""
echo "🔧 Useful commands:"
echo "- SSH into VM: vagrant ssh"
echo "- Check service status: sudo systemctl status sharetribe"
echo "- View logs: sudo journalctl -u sharetribe -f"
echo "- Rails console: cd /vagrant && bundle exec rails console"
