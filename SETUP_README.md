# Sharetribe Development Environment Setup

This guide provides multiple ways to set up a Sharetribe development environment with **Ruby 3.2.2** and **Rails *********.

## 🎯 Quick Start Options

### Option 1: Vagrant (Recommended for Development)

1. **Prerequisites:**
   ```bash
   # Install Vagrant and VirtualBox
   # macOS: brew install vagrant virtualbox
   # Ubuntu: sudo apt install vagrant virtualbox
   # Windows: Download from official websites
   ```

2. **Setup:**
   ```bash
   # Start Vagrant VM
   vagrant up
   
   # SSH into VM
   vagrant ssh
   
   # Run setup script
   cd /vagrant
   chmod +x vagrant_setup.sh
   ./vagrant_setup.sh
   
   # Start the server
   ./start_server.sh
   ```

3. **Access:**
   - Application: http://localhost:3001
   - From VM: http://localhost:3000

### Option 2: Docker (Recommended for Production-like Environment)

1. **Prerequisites:**
   ```bash
   # Install Docker and Docker Compose
   # macOS: brew install docker docker-compose
   # Ubuntu: sudo apt install docker.io docker-compose
   ```

2. **Setup:**
   ```bash
   # Build and start services
   docker-compose up --build
   
   # In another terminal, setup database
   docker-compose exec web bundle exec rake db:create db:migrate db:seed
   ```

3. **Access:**
   - Application: http://localhost:3001

### Option 3: Local Installation

1. **Prerequisites:**
   ```bash
   # Install system dependencies (Ubuntu/Debian)
   sudo apt-get update
   sudo apt-get install -y curl git build-essential libssl-dev libreadline-dev \
     zlib1g-dev libncurses5-dev libffi-dev libgdbm-dev libyaml-dev \
     libsqlite3-dev mysql-client libmysqlclient-dev imagemagick \
     libmagickwand-dev nodejs npm sphinxsearch mysql-server
   ```

2. **Setup:**
   ```bash
   # Make setup script executable
   chmod +x setup_sharetribe.sh
   
   # Run setup
   ./setup_sharetribe.sh
   
   # Start server
   bundle exec rails server -b 0.0.0.0 -p 3000
   ```

3. **Access:**
   - Application: http://localhost:3000

## 🔧 Manual Setup Steps

If you prefer to set up manually or troubleshoot issues:

### 1. Ruby Installation

```bash
# Install rbenv
curl -fsSL https://github.com/rbenv/rbenv-installer/raw/HEAD/bin/rbenv-installer | bash

# Add to PATH
export PATH="$HOME/.rbenv/bin:$PATH"
eval "$(rbenv init -)"

# Install Ruby 3.2.2
rbenv install 3.2.2
rbenv global 3.2.2
rbenv rehash

# Install Bundler
gem install bundler
```

### 2. Database Setup

```bash
# MySQL setup
sudo mysql -e "CREATE DATABASE sharetribe_development;"
sudo mysql -e "CREATE USER 'sharetribe'@'localhost' IDENTIFIED BY 'secret';"
sudo mysql -e "GRANT ALL PRIVILEGES ON sharetribe_development.* TO 'sharetribe'@'localhost';"
sudo mysql -e "FLUSH PRIVILEGES;"
```

### 3. Application Configuration

```bash
# Copy configuration files
cp config/database.example.yml config/database.yml
cp config/config.example.yml config/config.yml

# Edit database.yml with your MySQL credentials
# Edit config.yml with your domain settings
```

### 4. Dependencies and Database

```bash
# Install gems
bundle install

# Fix migrations (if needed)
ruby fix_migrations.rb

# Setup database
bundle exec rake db:create
bundle exec rake db:migrate
bundle exec rake db:seed
```

## 🚨 Troubleshooting

### Migration Issues

If you encounter migration errors:

```bash
# Fix migrations automatically
ruby fix_migrations.rb

# Try migrating again
bundle exec rake db:migrate

# If still failing, load schema directly
bundle exec rake db:schema:load
```

### Common Issues

1. **MySQL Connection Error:**
   - Ensure MySQL is running: `sudo systemctl start mysql`
   - Check credentials in `config/database.yml`

2. **Gem Installation Errors:**
   - Install missing system dependencies
   - Update bundler: `gem update bundler`

3. **JavaScript Runtime Error:**
   - Install Node.js: `sudo apt install nodejs npm`
   - Or add `mini_racer` gem to Gemfile

4. **Permission Errors:**
   - Ensure proper file permissions: `chmod +x *.sh`
   - Check rbenv permissions: `rbenv rehash`

### Reset Everything

```bash
# Vagrant
vagrant destroy && vagrant up

# Docker
docker-compose down -v && docker-compose up --build

# Local
bundle exec rake db:drop db:create db:migrate db:seed
```

## 📋 Verification

After setup, verify everything works:

```bash
# Check Ruby version
ruby -v  # Should show 3.2.2

# Check Rails version
bundle exec rails -v  # Should show *******

# Check database connection
bundle exec rails runner "puts ActiveRecord::Base.connection.adapter_name"

# Run tests (optional)
bundle exec rspec
```

## 🎉 Success!

If everything is working:
- ✅ Ruby 3.2.2 installed
- ✅ Rails ******* running
- ✅ MySQL database connected
- ✅ Application accessible at http://localhost:3000 or :3001

## 📚 Next Steps

1. Create your first admin user
2. Set up your first community
3. Configure payment gateways (if needed)
4. Customize the application
5. Deploy to production

## 🆘 Getting Help

If you encounter issues:
1. Check the logs: `tail -f log/development.log`
2. Verify database status: `bundle exec rake db:migrate:status`
3. Check service status: `sudo systemctl status mysql`
4. Review this README for troubleshooting steps

## 🔄 Updating

To update the application:
```bash
git pull origin master
bundle install
bundle exec rake db:migrate
bundle exec rake assets:precompile  # if needed
```
