#!/bin/bash

# Sharetribe Development Environment Setup Script
# This script sets up a complete Sharetribe development environment with Ruby 3.2.2 and Rails 7.2

set -e  # Exit on any error

echo "🚀 Starting Sharetribe Development Environment Setup..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're running inside Vagrant
if [ -d "/vagrant" ]; then
    cd /vagrant
    log_info "Running inside Vagrant VM"
else
    log_info "Running on host machine"
fi

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 1. Install system dependencies
log_info "Installing system dependencies..."
if command_exists apt-get; then
    sudo apt-get update
    sudo apt-get install -y \
        curl \
        git \
        build-essential \
        libssl-dev \
        libreadline-dev \
        zlib1g-dev \
        libncurses5-dev \
        libffi-dev \
        libgdbm-dev \
        libyaml-dev \
        libsqlite3-dev \
        libgdbm-compat-dev \
        libncurses5-dev \
        libreadline6-dev \
        uuid-dev \
        mysql-client \
        libmysqlclient-dev \
        imagemagick \
        libmagickwand-dev \
        nodejs \
        npm \
        sphinxsearch
elif command_exists yum; then
    sudo yum update -y
    sudo yum groupinstall -y "Development Tools"
    sudo yum install -y \
        curl \
        git \
        openssl-devel \
        readline-devel \
        zlib-devel \
        ncurses-devel \
        libffi-devel \
        gdbm-devel \
        libyaml-devel \
        sqlite-devel \
        mysql-devel \
        ImageMagick-devel \
        nodejs \
        npm \
        sphinx
fi

# 2. Install rbenv if not present
if ! command_exists rbenv; then
    log_info "Installing rbenv..."
    curl -fsSL https://github.com/rbenv/rbenv-installer/raw/HEAD/bin/rbenv-installer | bash
    export PATH="$HOME/.rbenv/bin:$PATH"
    eval "$(rbenv init -)"
    echo 'export PATH="$HOME/.rbenv/bin:$PATH"' >> ~/.bashrc
    echo 'eval "$(rbenv init -)"' >> ~/.bashrc
else
    log_info "rbenv already installed"
    export PATH="$HOME/.rbenv/bin:$PATH"
    eval "$(rbenv init -)"
fi

# 3. Install Ruby 3.2.2
log_info "Installing Ruby 3.2.2..."
if ! rbenv versions | grep -q "3.2.2"; then
    rbenv install 3.2.2
fi
rbenv global 3.2.2
rbenv rehash

# Verify Ruby installation
ruby_version=$(ruby -v)
log_success "Ruby installed: $ruby_version"

# 4. Install Bundler
log_info "Installing Bundler..."
gem install bundler
rbenv rehash

# 5. Install gems
log_info "Installing Ruby gems..."
if [ -f "Gemfile" ]; then
    bundle install
    log_success "Gems installed successfully"
else
    log_error "Gemfile not found!"
    exit 1
fi

# 6. Setup database configuration
log_info "Setting up database configuration..."
if [ ! -f "config/database.yml" ]; then
    log_info "Creating database.yml from template..."
    cp config/database.example.yml config/database.yml 2>/dev/null || true
fi

# 7. Setup application configuration
log_info "Setting up application configuration..."
if [ ! -f "config/config.yml" ]; then
    log_info "Creating config.yml from template..."
    cp config/config.example.yml config/config.yml 2>/dev/null || true
fi

# 8. Fix migrations
log_info "Fixing database migrations..."
if [ -f "fix_migrations.rb" ]; then
    ruby fix_migrations.rb
    log_success "Migrations fixed"
fi

# 9. Database setup
log_info "Setting up database..."

# Check if MySQL is running
if ! pgrep -x "mysqld" > /dev/null; then
    log_warning "MySQL not running. Please start MySQL service first."
    log_info "On Ubuntu/Debian: sudo systemctl start mysql"
    log_info "On CentOS/RHEL: sudo systemctl start mysqld"
fi

# Create database
log_info "Creating database..."
bundle exec rake db:create 2>/dev/null || log_warning "Database might already exist"

# Run migrations with error handling
log_info "Running database migrations..."
if ! bundle exec rake db:migrate; then
    log_warning "Some migrations failed. Attempting to continue with schema load..."
    
    # Alternative: Load schema directly
    if [ -f "db/schema.rb" ]; then
        log_info "Loading database schema..."
        bundle exec rake db:schema:load
    fi
fi

# 10. Seed database (optional)
log_info "Seeding database..."
bundle exec rake db:seed 2>/dev/null || log_warning "Database seeding failed or not needed"

# 11. Precompile assets (for production-like setup)
log_info "Precompiling assets..."
RAILS_ENV=development bundle exec rake assets:precompile 2>/dev/null || log_warning "Asset precompilation failed"

# 12. Setup Sphinx search (optional)
log_info "Setting up Sphinx search..."
if command_exists searchd; then
    bundle exec rake ts:configure 2>/dev/null || log_warning "Sphinx configuration failed"
    bundle exec rake ts:index 2>/dev/null || log_warning "Sphinx indexing failed"
    bundle exec rake ts:start 2>/dev/null || log_warning "Sphinx start failed"
else
    log_warning "Sphinx not installed, skipping search setup"
fi

log_success "🎉 Sharetribe setup completed!"
echo ""
echo "📋 Next steps:"
echo "1. Start the Rails server: bundle exec rails server -b 0.0.0.0 -p 3000"
echo "2. Visit http://localhost:3000 (or http://localhost:3001 if using Vagrant)"
echo "3. Create your first admin user and community"
echo ""
echo "🔧 Useful commands:"
echo "- Run tests: bundle exec rspec"
echo "- Rails console: bundle exec rails console"
echo "- Check logs: tail -f log/development.log"
echo ""
echo "🐛 If you encounter issues:"
echo "- Check database connection in config/database.yml"
echo "- Ensure MySQL is running"
echo "- Run: bundle exec rake db:migrate:status to check migration status"
