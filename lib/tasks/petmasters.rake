namespace :petmasters do

  desc 'Rebuild the Location-Based search term redirect database'
  task rebuild_location_based_search_terms: :environment do
    LocationBasedSearchTerm.rebuild_lookup_table
  end

  desc 'Send daily activity report'
  task :send_daily_activity_report, [:email] => :environment do |t, args|
    email = args.fetch(:email) { "<EMAIL>" }
    community = Community.find_by_ident("petworks")
    MailCarrier.deliver_now(CommunityMailer.daily_activity_report(email, community))
  end

  desc 'Enable the Pricing Packages Feature Flag'
  task enable_pricing_packages: :environment do
    FeatureFlagService::Api::Api.features.enable(community_id: Community.find_by_ident("petworks").id, features: [:pm_pricing_packages])
    status = FeatureFlagService::Api::Api.features.get_for_community(community_id: Community.find_by_ident("petworks").id).data[:features].include?(:pm_pricing_packages)
    puts "Pricing Packages is #{status ? "Enabled" : "Disabled"}"
  end

  desc 'Disable the Pricing Packages Feature Flag'
  task disable_pricing_packages: :environment do
    FeatureFlagService::API::Api.features.disable(community_id: Community.find_by_ident("petworks").id, features: [:pm_pricing_packages])
    status = FeatureFlagService::API::Api.features.get_for_community(community_id: Community.find_by_ident("petworks").id).data[:features].include?(:pm_pricing_packages)
    puts "Pricing Packages is #{status ? "Enabled" : "Disabled"}"
  end

  desc 'Runs Booking Percentile Report'
  task booking_percentile_report: :environment do
    BookingPercentileHelper.calculate_booking_percentile
  end
end
