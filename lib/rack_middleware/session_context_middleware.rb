#
# This middleware stores session context to RequestStore
#

# Require dependencies explicitly since middleware loads before Rails autoloader
require_relative '../../app/utils/entity_utils'
require_relative '../../app/services/session_context_store'

class SessionContextMiddleware

  def initialize(app)
    @app = app
  end

  def call(env)
    ::SessionContextStore.set_from_model(
      community: env[:current_marketplace],
      person: env["warden"]&.user)

    @app.call(env)
  end
end
