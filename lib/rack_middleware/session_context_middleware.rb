#
# This middleware stores session context to RequestStore
#

# Require dependencies explicitly since middleware loads before Rails autoloader
require_relative '../../app/utils/entity_utils'
require_relative '../../app/services/session_context_store'

class SessionContextMiddleware

  def initialize(app)
    @app = app
  end

  def call(env)
    begin
      ::SessionContextStore.set_from_model(
        community: env[:current_marketplace],
        person: env["warden"]&.user)
    rescue => e
      # Log the error but don't fail the request
      Rails.logger.error("SessionContextMiddleware error: #{e.message}") if defined?(Rails.logger)
      # Continue without setting session context
    end

    @app.call(env)
  end
end
