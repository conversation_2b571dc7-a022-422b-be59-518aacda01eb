#!/usr/bin/env ruby

# Script to fix common migration issues in the Sharetribe codebase
# This addresses the most common patterns we've seen

require 'fileutils'

def fix_migration_file(file_path)
  puts "Fixing #{file_path}..."
  content = File.read(file_path)
  original_content = content.dup

  # Fix syntax errors with => in wrong places
  content.gsub!(/if column_exists\?\([^)]+\)\s*=>\s*[^,\s]+/, '')
  content.gsub!(/remove_column[^=]*=>\s*[^,\s\n]+/, '')

  # Fix corrupted column names in remove_column calls (like :priva)c)y -> :privacy)
  content.gsub!(/remove_column\s+:(\w+),\s+:(\w+)\s+if column_exists\?\(:\1,\s+:\2\)([a-z]+)\s+if column_exists\?\(:\1,\s+:\2\3\)([a-z]+)\s+if column_exists\?\(:\1,\s+:\2\3\4\)/) do |match|
    table = $1
    partial_column = $2
    suffix1 = $3
    suffix2 = $4
    full_column = "#{partial_column}#{suffix1}#{suffix2}"
    "remove_column :#{table}, :#{full_column} if column_exists?(:#{table}, :#{full_column})"
  end

  # Fix corrupted column names (like :queu)e -> :queue)
  content.gsub!(/remove_column\s+:(\w+),\s+:(\w+)\s+if column_exists\?\(:\1,\s+:\2\)([a-z]+)\s+if column_exists\?\(:\1,\s+:\2\3\)/) do |match|
    table = $1
    partial_column = $2
    suffix = $3
    full_column = "#{partial_column}#{suffix}"
    "remove_column :#{table}, :#{full_column} if column_exists?(:#{table}, :#{full_column})"
  end

  # Fix incomplete add_column calls
  content.gsub!(/add_column\s+:[^,\s]+\s*$/, '')
  content.gsub!(/add_column\s+:[^,\s]+\s*\n/, '')

  # Fix incomplete remove_column calls
  content.gsub!(/remove_column\s+:[^,\s]+\s*$/, '')
  content.gsub!(/remove_column\s+:[^,\s]+\s*\n/, '')

  # Fix broken remove_column calls with invalid column names
  content.gsub!(/remove_column\s+:(\w+),\s+:(boolean|integer|string|text|default|float)\b/, '')

  # Fix PostgreSQL-style ALTER TABLE syntax
  content.gsub!(/execute\s+"ALTER TABLE (\w+) ALTER COLUMN (\w+) TYPE (\w+) USING [^"]*"/) do |match|
    table = $1
    column = $2
    type = $3
    "change_column :#{table}, :#{column}, :#{type}"
  end

  # Fix TEXT columns with default values (MySQL doesn't support this)
  content.gsub!(/:text,\s*:default\s*=>\s*"[^"]*"/, ':text')
  content.gsub!(/:text,\s*{[^}]*:default[^}]*}/, ':text')

  # Add defensive column_exists? checks to remove_column calls that don't have them
  content.gsub!(/^(\s*)remove_column\s+:(\w+),\s+:(\w+)(?!\s+if)/) do |match|
    indent = $1
    table = $2
    column = $3
    "#{indent}remove_column :#{table}, :#{column} if column_exists?(:#{table}, :#{column})"
  end

  # Fix broken down methods with string literals as remove_column calls
  content.gsub!(/remove_column\s+:[^,]+,\s+"[^"]*"/, '')

  # Fix broken raise statements
  content.gsub!(/raise\s+ActiveRecord::IrreversibleMigration\s*\n\s*remove_column.*/, 'raise ActiveRecord::IrreversibleMigration, "This migration cannot be reversed"')

  # Clean up multiple consecutive blank lines
  content.gsub!(/\n\s*\n\s*\n+/, "\n\n")

  # Remove lines that are just whitespace or incomplete statements
  lines = content.split("\n")
  cleaned_lines = lines.reject do |line|
    line.strip.empty? ||
    line.strip == 'remove_column' ||
    line.strip == 'add_column' ||
    line.strip.match?(/^(remove_column|add_column)\s*$/) ||
    line.strip.match?(/^remove_column\s+:[^,]+,\s*$/) ||
    line.strip.match?(/^remove_column.*".*".*$/) ||
    line.strip.match?(/^#\s*$/)
  end

  content = cleaned_lines.join("\n") + "\n"

  # Only write if content changed
  if content != original_content
    File.write(file_path, content)
    puts "  Fixed!"
  else
    puts "  No changes needed"
  end
end

# Find all migration files
migration_dir = 'db/migrate'
if Dir.exist?(migration_dir)
  Dir.glob("#{migration_dir}/*.rb").each do |file|
    fix_migration_file(file)
  end
  puts "Migration fixing complete!"
else
  puts "Migration directory not found: #{migration_dir}"
end
