#!/usr/bin/env ruby

# Fix corrupted migration files with specific patterns
# This script fixes the pattern where column names are split across multiple if statements

require 'fileutils'

def fix_corrupted_migration(file_path)
  puts "Checking #{file_path}..."
  content = File.read(file_path)
  original_content = content.dup
  
  # Pattern 1: :privac)y -> :privacy
  # remove_column :table, :privac if column_exists?(:table, :privac)y if column_exists?(:table, :privacy)
  content.gsub!(/remove_column\s+:(\w+),\s+:(\w+)\s+if column_exists\?\(:\1,\s+:\2\)([a-z]+)\s+if column_exists\?\(:\1,\s+:\2\3\)/) do |match|
    table = $1
    partial_column = $2
    suffix = $3
    full_column = "#{partial_column}#{suffix}"
    "remove_column :#{table}, :#{full_column} if column_exists?(:#{table}, :#{full_column})"
  end
  
  # Pattern 2: :community_i)d -> :community_id
  # remove_column :table, :community_i if column_exists?(:table, :community_i)d if column_exists?(:table, :community_id)
  content.gsub!(/remove_column\s+:(\w+),\s+:(\w+)\s+if column_exists\?\(:\1,\s+:\2\)([a-z]+)\s+if column_exists\?\(:\1,\s+:\2\3\)/) do |match|
    table = $1
    partial_column = $2
    suffix = $3
    full_column = "#{partial_column}#{suffix}"
    "remove_column :#{table}, :#{full_column} if column_exists?(:#{table}, :#{full_column})"
  end
  
  # Pattern 3: :queu)e -> :queue
  # remove_column :table, :queu if column_exists?(:table, :queu)e if column_exists?(:table, :queue)
  content.gsub!(/remove_column\s+:(\w+),\s+:(\w+)\s+if column_exists\?\(:\1,\s+:\2\)([a-z]+)\s+if column_exists\?\(:\1,\s+:\2\3\)/) do |match|
    table = $1
    partial_column = $2
    suffix = $3
    full_column = "#{partial_column}#{suffix}"
    "remove_column :#{table}, :#{full_column} if column_exists?(:#{table}, :#{full_column})"
  end
  
  # More general pattern for any corrupted column name
  # This handles cases where the column name is split and reassembled incorrectly
  content.gsub!(/remove_column\s+:(\w+),\s+:([a-z_]+)\s+if column_exists\?\(:\1,\s+:\2\)([a-z_]+)\s+if column_exists\?\(:\1,\s+:\2\3\)/) do |match|
    table = $1
    partial_column = $2
    suffix = $3
    full_column = "#{partial_column}#{suffix}"
    "remove_column :#{table}, :#{full_column} if column_exists?(:#{table}, :#{full_column})"
  end
  
  # Handle cases with multiple suffixes (like :priva)c)y)
  content.gsub!(/remove_column\s+:(\w+),\s+:([a-z_]+)\s+if column_exists\?\(:\1,\s+:\2\)([a-z_]+)\s+if column_exists\?\(:\1,\s+:\2\3\)([a-z_]+)\s+if column_exists\?\(:\1,\s+:\2\3\4\)/) do |match|
    table = $1
    partial_column = $2
    suffix1 = $3
    suffix2 = $4
    full_column = "#{partial_column}#{suffix1}#{suffix2}"
    "remove_column :#{table}, :#{full_column} if column_exists?(:#{table}, :#{full_column})"
  end
  
  if content != original_content
    File.write(file_path, content)
    puts "  Fixed corruption in #{file_path}"
  else
    puts "  No corruption found in #{file_path}"
  end
end

# Find all migration files
migration_files = Dir.glob("db/migrate/*.rb").sort

puts "Fixing corrupted migrations..."
migration_files.each do |file|
  fix_corrupted_migration(file)
end

puts "Corruption fixing complete!"
