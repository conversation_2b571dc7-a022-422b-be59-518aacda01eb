Vagrant.configure('2') do |config|
  config.vm.box = 'ubuntu/xenial64'
  config.vm.provider 'virtualbox' do |v|
    v.name = 'PetMasters Development'
    v.memory = 4096
    v.cpus = 2

    # disable console log from base xenial box
    v.customize [ 'modifyvm', :id, '--uartmode1', 'disconnected' ]
  end

  # Use VirtualBox shared folders instead of NFS
  config.vm.synced_folder ".", "/vagrant", type: "virtualbox"

  # install dependencies
  config.vm.provision 'shell', privileged: true, inline: 'apt-get update'
  config.vm.provision 'shell', privileged: true, inline: 'DEBIAN_FRONTEND=noninteractive apt-get -y install libssl-dev libreadline-dev zlib1g-dev build-essential mysql-client-5.7 mysql-server-5.7 libmysqlclient-dev libsqlite3-dev imagemagick libffi-dev libyaml-dev'

  # install sphinxsearch via package manager (simpler approach)
  config.vm.provision 'shell', privileged: true, inline: 'apt-get -y install sphinxsearch || echo "Sphinx installation failed, continuing without it"'

  # install nvm
  config.vm.provision 'shell', privileged: false, inline: %q(curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.5/install.sh | bash)
  config.vm.provision 'shell', privileged: false, inline: %q(bash -ic 'nvm install 18.18.0' || true)

    # download and install rbenv
  config.vm.provision 'shell', privileged: false, inline: 'git clone https://github.com/rbenv/rbenv.git ~/.rbenv'
  config.vm.provision 'shell', privileged: false, inline: 'cd ~/.rbenv && src/configure && make -C src'
  config.vm.provision 'shell', privileged: false, inline: %q(echo 'export PATH="$HOME/.rbenv/bin:$PATH"' >> ~/.bashrc)
  config.vm.provision 'shell', privileged: false, inline: %q(echo 'eval "$(rbenv init -)"' >> ~/.bashrc)

  # download and install rbenv build
  config.vm.provision 'shell', privileged: false, inline: 'mkdir -p ~/.rbenv/plugins'
  config.vm.provision 'shell', privileged: false, inline: 'git clone https://github.com/rbenv/ruby-build.git ~/.rbenv/plugins/ruby-build'

  # download and install ruby MRI 3.2.2
  config.vm.provision 'shell', privileged: false, inline: 'eval "$(PATH=${HOME}/.rbenv/bin:${PATH} rbenv init -)"; PATH=${HOME}/.rbenv/bin:${PATH} rbenv install 3.2.2'
  config.vm.provision 'shell', privileged: false, inline: 'eval "$(PATH=${HOME}/.rbenv/bin:${PATH} rbenv init -)"; PATH=${HOME}/.rbenv/bin:${PATH} rbenv global 3.2.2'

  # install bundle
  config.vm.provision 'shell', privileged: false, inline: 'eval "$(PATH=${HOME}/.rbenv/bin:${PATH} rbenv init -)"; PATH=${HOME}/.rbenv/bin:${PATH} gem install bundler:2.4.10 foreman mailcatcher webrick --no-document'
  config.vm.provision 'shell', privileged: false, inline: 'cd /vagrant; eval "$(PATH=${HOME}/.rbenv/bin:${PATH} rbenv init -)"; PATH=${HOME}/.rbenv/bin:${PATH} bundle install'

  # create database user
  config.vm.provision 'shell', privileged: true, inline: %q(mysql -e "create user 'vagrant'@'%'")
  config.vm.provision 'shell', privileged: true, inline: %q(mysql -e "grant all privileges on *.* to 'vagrant'@'%'")

  # configure and load database
  config.vm.provision 'shell', privileged: false, inline: 'cp /vagrant/config/database.example.yml /vagrant/config/database.yml'
  config.vm.provision 'shell', privileged: false, inline: 'cd /vagrant; eval "$(PATH=${HOME}/.rbenv/bin:${PATH} rbenv init -)"; PATH=${HOME}/.rbenv/bin:${PATH} bundle exec rake db:create db:structure:load db:migrate'
  config.vm.provision 'shell', privileged: false, inline: 'cd /vagrant; RAILS_ENV=development rake db:environment:set'

  # use the default application configuration
  config.vm.provision 'shell', privileged: false, inline: 'cp /vagrant/config/config.defaults.yml /vagrant/config/config.yml'

  # build the UI
  # IMPORTANT!  If a Windows Host, the (windows) user account
  # launching the VM must have the SeCreateSymbolicLinkPrivilege in
  # order for the node build to create symlinks under /vagrant which
  # is shared with the host platform
  config.vm.provision 'shell', privileged: false, inline: %q(bash -ic 'cd /vagrant && npm install' || true)

  # port forwarding
  config.vm.network 'forwarded_port', id: 'rails dev', guest: 3000, host: 3001
  config.vm.network 'forwarded_port', id: 'mysql', guest: 3306, host: 3307
  config.vm.network 'forwarded_port', id: 'mailcatcher', guest: 1080, host: 1080

  # Post install setup
  config.vm.provision 'shell', privileged: false, inline: %q(bash -ic 'cd /vagrant && ./setup-dev-db.sh')

end
