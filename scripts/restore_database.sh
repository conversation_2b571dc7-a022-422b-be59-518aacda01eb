#!/bin/bash

# Petworks Database Restoration Script
# This script restores the Petworks database from the provided SQL dump

set -e

echo "🔄 Restoring Petworks database..."

# Check if db.sql exists
if [ ! -f "db.sql" ]; then
    echo "❌ Error: db.sql file not found in current directory"
    echo "Please ensure db.sql is present before running this script"
    exit 1
fi

# Drop and recreate the database
echo "📦 Dropping and recreating database..."
mysql -u vagrant -e "DROP DATABASE IF EXISTS sharetribe_development; CREATE DATABASE sharetribe_development CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# Restore from SQL dump
echo "📥 Restoring data from db.sql..."
mysql -u vagrant sharetribe_development < db.sql

# Run any pending migrations
echo "🔧 Running database migrations..."
bundle exec rails db:migrate

echo "✅ Database restoration completed successfully!"
echo "🎉 Petworks database is now ready with community ID 26309"
