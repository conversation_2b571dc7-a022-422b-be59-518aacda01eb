class RemoveUnusedColumnsFromListings < ActiveRecord::Migration[7.2]
  def self.up
            remove_column :listings, :content if column_exists?(:listings, :content)
    remove_column :listings, :good_thru if column_exists?(:listings, :good_thru)
    remove_column :listings, :status if column_exists?(:listings, :status)
    remove_column :listings, :value_cc if column_exists?(:listings, :value_cc)
    remove_column :listings, :value_other if column_exists?(:listings, :value_other)
  end
  def self.down
    remove_column :listings, :content if column_exists?(:listings, :content)
     if column_exists?(:listings, :text)
    remove_column :listings, :good_thru if column_exists?(:listings, :good_thru)
    remove_column :listings, :date if column_exists?(:listings, :date)
    remove_column :listings, :status if column_exists?(:listings, :status)
     if column_exists?(:listings, :string)
    remove_column :listings, :value_cc if column_exists?(:listings, :value_cc)
     if column_exists?(:listings, :integer)
    remove_column :listings, :value_other if column_exists?(:listings, :value_other)
     if column_exists?(:listings, :string)    
  end
end
