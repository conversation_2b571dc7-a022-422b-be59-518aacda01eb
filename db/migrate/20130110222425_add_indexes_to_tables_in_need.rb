class AddIndexesToTablesInNeed < ActiveRecord::Migration[7.2]
  def change
    add_index :community_memberships, [:person_id, :community_id], :name => "memberships" unless index_exists?(:community_memberships, [:person_id, :community_id])
    add_index :participations, :person_id if column_exists?(:participations, :person_id) && !index_exists?(:participations, :person_id)
    add_index :participations, :conversation_id if column_exists?(:participations, :conversation_id) && !index_exists?(:participations, :conversation_id)
    add_index :badges, :person_id if column_exists?(:badges, :person_id) && !index_exists?(:badges, :person_id)
    add_index :communities, :domain if column_exists?(:communities, :domain) && !index_exists?(:communities, :domain)
    add_index :listing_followers, :listing_id if column_exists?(:listing_followers, :listing_id) && !index_exists?(:listing_followers, :listing_id)
    add_index :listings, :category if column_exists?(:listings, :category) && !index_exists?(:listings, :category)
    add_index :listings, :share_type if column_exists?(:listings, :share_type) && !index_exists?(:listings, :share_type)
    add_index :locations, :person_id if column_exists?(:locations, :person_id) && !index_exists?(:locations, :person_id)
    add_index :locations, :listing_id if column_exists?(:locations, :listing_id) && !index_exists?(:locations, :listing_id)
    add_index :locations, :community_id if column_exists?(:locations, :community_id) && !index_exists?(:locations, :community_id)
    add_index :messages, :conversation_id if column_exists?(:messages, :conversation_id) && !index_exists?(:messages, :conversation_id)
    add_index :notifications, :receiver_id if column_exists?(:notifications, :receiver_id) && !index_exists?(:notifications, :receiver_id)
    add_index :statistics, :community_id if column_exists?(:statistics, :community_id) && !index_exists?(:statistics, :community_id)
    add_index :testimonials, :receiver_id if column_exists?(:testimonials, :receiver_id) && !index_exists?(:testimonials, :receiver_id)
  end
end
