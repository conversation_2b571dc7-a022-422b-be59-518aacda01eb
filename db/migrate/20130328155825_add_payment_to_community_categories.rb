class AddPaymentToCommunityCategories < ActiveRecord::Migration[7.2]
  def up
    add_column :community_categories, :payment, :boolean, :default => false unless column_exists?(:community_categories, :payment)
    # CategoriesHelper.add_custom_price_quantity_placeholders - Skipping as helper doesn't exist
  end
  
  def down
    remove_column :community_categories, :payment if column_exists?(:community_categories, :payment)
  end
end
