class AddAuthorNameToListing < ActiveRecord::Migration[5.1]
  def up
    add_column :listings, :author_name, :string
    Listing.all.each do |listing|
      unless listing.deleted? || listing.author.nil?
        begin
          listing.update_column(:author_name, listing.author.public_name[0..254])
        rescue => e
          Rails.logger.error e
        end
      end
    end
  end
  def down
    remove_column :listings, :author_name if column_exists?(:listings, :author_name)
  end
end
