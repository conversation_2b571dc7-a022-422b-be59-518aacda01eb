class CopyUuidsToTransactions < ActiveRecord::Migration[7.2]
  def up
    # PostgreSQL uses different syntax for multi-table updates
    execute "UPDATE transactions SET listing_uuid = listings.uuid FROM listings WHERE transactions.listing_id = listings.id"
    execute "UPDATE transactions SET community_uuid = communities.uuid FROM communities WHERE transactions.community_id = communities.id"
  end
  def down
    execute "UPDATE transactions SET listing_uuid = NULL, community_uuid = NULL"
  end
end
