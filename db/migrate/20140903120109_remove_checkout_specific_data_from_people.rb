class RemoveCheckoutSpecificDataFromPeople < ActiveRecord::Migration[7.2]
  def up
            remove_column :people, :checkout_merchant_id if column_exists?(:people, :checkout_merchant_id)
    remove_column :people, :checkout_merchant_key if column_exists?(:people, :checkout_merchant_key)
    remove_column :people, :company_id if column_exists?(:people, :company_id)
  end
  def down
    remove_column :people, :company_id if column_exists?(:people, :company_id)
     if column_exists?(:people, :string)
    remove_column :people, :checkout_merchant_id if column_exists?(:people, :checkout_merchant_id)
     if column_exists?(:people, :string)
    remove_column :people, :checkout_merchant_key if column_exists?(:people, :checkout_merchant_key)
     if column_exists?(:people, :string)
  end
end
