class PopulateEndOnExclusive < ActiveRecord::Migration[7.2]
  def up
    name = "Populate end_on_exclusive for day bookings"
    # PostgreSQL uses different syntax for UPDATE with JOIN and date functions
    exec_update([
                  "UPDATE bookings",
                  "SET end_on_exclusive = ",
                  "CASE transactions.unit_type",
                  "WHEN 'day' THEN bookings.end_on + INTERVAL '1 day'",
                  "ELSE bookings.end_on",
                  "END",
                  "FROM transactions",
                  "WHERE transactions.id = bookings.transaction_id"
                ].join(" "), name, [])
  end
  def down
    name = "Rollback populate end_on_exclusive for day bookings"
    # PostgreSQL uses different syntax for UPDATE with JOIN and date functions
    exec_update([
                  "UPDATE bookings",
                  "SET end_on = ",
                  "CASE transactions.unit_type",
                  "WHEN 'day' THEN bookings.end_on_exclusive - INTERVAL '1 day'",
                  "ELSE bookings.end_on_exclusive",
                  "END",
                  "FROM transactions",
                  "WHERE transactions.id = bookings.transaction_id"
                ].join(" "), name, [])
  end
end
