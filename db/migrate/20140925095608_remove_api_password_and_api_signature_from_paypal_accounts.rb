class RemoveApiPasswordAndApiSignatureFromPaypalAccounts < ActiveRecord::Migration[7.2]
  def up
            remove_column :paypal_accounts, :api_password if column_exists?(:paypal_accounts, :api_password)
    remove_column :paypal_accounts, :api_signature if column_exists?(:paypal_accounts, :api_signature)
  end
  def down
    remove_column :paypal_accounts, :api_password if column_exists?(:paypal_accounts, :api_password)
     if column_exists?(:paypal_accounts, :string)
    remove_column :paypal_accounts, :api_signature if column_exists?(:paypal_accounts, :api_signature)
     if column_exists?(:paypal_accounts, :string)
  end
end
