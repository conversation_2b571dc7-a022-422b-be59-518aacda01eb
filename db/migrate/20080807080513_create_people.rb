class CreatePeople < ActiveRecord::Migration[7.2]
  
  def self.up
    create_table :people, :id => false do |t|
      t.string :id, :limit => 22, :null => false
      t.integer :coin_amount, :null => false, :default => 0
      t.timestamps
    end
    # SQLite compatible way to set primary key
    if ActiveRecord::Base.connection.adapter_name == 'SQLite'
      # For SQLite, we need to recreate the table with proper primary key
      execute <<-SQL
        CREATE TABLE people_new (
          id varchar(22) NOT NULL PRIMARY KEY,
          coin_amount integer DEFAULT 0 NOT NULL,
          created_at datetime(6) NOT NULL,
          updated_at datetime(6) NOT NULL
        )
      SQL
      execute "INSERT INTO people_new SELECT * FROM people"
      execute "DROP TABLE people"
      execute "ALTER TABLE people_new RENAME TO people"
    else
      execute "ALTER TABLE people ADD PRIMARY KEY (id)"
    end
  end

  def self.down
    drop_table :people
  end
end

