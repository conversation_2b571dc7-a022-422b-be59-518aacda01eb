class EditPersonComment < ActiveRecord::Migration[7.2]
  def self.up
    remove_column :person_comments, :task_type if column_exists?(:person_comments, :task_type)
    remove_column :person_comments, :task_id if column_exists?(:person_comments, :task_id)
    change_column :person_comments, :grade, :float if column_exists?(:person_comments, :grade)
  end
  def self.down
    add_column :person_comments, :task_type, :string
    add_column :person_comments, :task_id, :integer
    change_column :person_comments, :grade, :integer if column_exists?(:person_comments, :grade)
  end
end
