class DropOldUnusedColumns < ActiveRecord::Migration[7.2]
  def up
    # Remove old unused columns if they exist
            remove_column :communities, :dv_test_file_name if column_exists?(:communities, :dv_test_file_name) if column_exists?(:communities
    remove_column :communities, :dv_test_file_name if column_exists?(:communities, :dv_test_file_name))
    remove_column :communities, :dv_test_file_name if column_exists?(:communities, :dv_test_file_name) if column_exists?(:communities, :dv_test_file_name)
  end
  def down
    # Add back the columns if needed for rollback
    add_column :communities, :dv_test_file, :string, limit: 64 unless column_exists?(:communities, :dv_test_file)
    add_column :communities, :dv_test_file_name, :string, limit: 64 unless column_exists?(:communities, :dv_test_file_name)
  end
end
