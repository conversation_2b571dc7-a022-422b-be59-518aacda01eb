class RemoveTransactionColumnsFromConversation < ActiveRecord::Migration[7.2]
  def up
            remove_column :conversations, :type if column_exists?(:conversations, :type)
    remove_column :conversations, :automatic_confirmation_after_days if column_exists?(:conversations, :automatic_confirmation_after_days)
  end
  def down
    remove_column :conversations, :type if column_exists?(:conversations, :type)
     if column_exists?(:conversations, :string)
    remove_column :conversations, :automatic_confirmation_after_days if column_exists?(:conversations, :automatic_confirmation_after_days)
     if column_exists?(:conversations, :integer)
  end
end
