class FixReservedWordFromLocation < ActiveRecord::Migration[7.2]
  def self.up
  	        remove_column :locations, :type if column_exists?(:locations, :type)
    remove_column :locations, :location_type if column_exists?(:locations, :location_type)
     if column_exists?(:locations, :string)
  end
  def self.down
    remove_column :locations, :location_type if column_exists?(:locations, :location_type)
    remove_column :locations, :type if column_exists?(:locations, :type)
     if column_exists?(:locations, :string)
  end
end
