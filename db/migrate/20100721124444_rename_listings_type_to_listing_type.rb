class RenameListingsTypeToListingType < ActiveRecord::Migration[7.2]
  def self.up
            remove_column :listings, :listings_type if column_exists?(:listings, :listings_type)
    remove_column :listings, :listing_type if column_exists?(:listings, :listing_type)
     if column_exists?(:listings, :string)
  end
  def self.down
    remove_column :listings, :listing_type if column_exists?(:listings, :listing_type)
    remove_column :listings, :listings_type if column_exists?(:listings, :listings_type)
     if column_exists?(:listings, :string)
  end
end
