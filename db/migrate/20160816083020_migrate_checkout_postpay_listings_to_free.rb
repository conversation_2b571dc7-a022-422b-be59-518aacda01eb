class MigrateCheckoutPostpayListingsToFree < ActiveRecord::Migration[7.2]
  def up
    # 1) Find all listings where transaction process is "postpay" and payment gateway is "Checkout"
    # 2) Change the listings' transaction process to "none"
    execute(<<-SQL)
      UPDATE listings
      SET transaction_process_id = (
        SELECT id FROM transaction_processes
        WHERE community_id = listings.community_id
        AND process = 'none'
        AND author_is_seller = true
      )
      FROM transaction_processes txp_current, payment_gateways pg
      WHERE txp_current.id = listings.transaction_process_id
      AND pg.community_id = listings.community_id
      AND txp_current.process = 'postpay'
      AND pg.type = 'Checkout'
    SQL
  end
  def down
    # Nothing here
  end
end
