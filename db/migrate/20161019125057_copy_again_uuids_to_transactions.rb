class CopyAgainUuidsToTransactions < ActiveRecord::Migration[7.2]
  def up
    # PostgreSQL uses different syntax for multi-table updates
    execute "UPDATE transactions SET listing_uuid = listings.uuid FROM listings WHERE transactions.listing_id = listings.id"
    execute "UPDATE transactions SET community_uuid = communities.uuid FROM communities WHERE transactions.community_id = communities.id"
  end
  def down
    # No-op. We lost the previous data so there's nothing we can do.
  end
end
