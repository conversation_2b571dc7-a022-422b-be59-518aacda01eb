class SwitchLocationIds < ActiveRecord::Migration[7.2]
  def self.up
          remove_column :people, :location_id if column_exists?(:people, :location_id)
    remove_column :people, :origin_loc_id if column_exists?(:people, :origin_loc_id)
    remove_column :people, :destination_loc_id if column_exists?(:people, :destination_loc_id)
    remove_column :people, :person_id if column_exists?(:people, :person_id)
     if column_exists?(:people, :integer)
    remove_column :people, :listing_id if column_exists?(:people, :listing_id)
     if column_exists?(:people, :integer)
  end
  def self.down
    remove_column :people, :location_id if column_exists?(:people, :location_id)
    remove_column :people, :origin_loc_id if column_exists?(:people, :origin_loc_id)
    remove_column :people, :destination_loc_id if column_exists?(:people, :destination_loc_id)
    remove_column :people, :person_id if column_exists?(:people, :person_id)
    remove_column :people, :listing_id if column_exists?(:people, :listing_id)
  end
end
