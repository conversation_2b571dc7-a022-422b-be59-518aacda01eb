class CreateTransactionProcessTokens < ActiveRecord::Migration[7.2]
  def up
    create_table :transaction_process_tokens do |t|
      t.column :community_id, :integer, null: false
      t.column :transaction_id, :integer, null: false
      t.column :op_completed, :boolean, null: false, default: false
      t.column :op_name, :string, limit: 64, null: false
      t.column :op_input, :text
      t.column :op_output, :text
      t.timestamps null: false
    end
    # PostgreSQL uses BYTEA for binary data instead of MySQL's BINARY type
    # PostgreSQL doesn't support AFTER clause, so column will be added at the end
    add_column :transaction_process_tokens, :process_token, :binary, limit: 16
    add_index :transaction_process_tokens, :process_token, unique: true
    # Handle potential duplicate index error
    begin
      add_index :transaction_process_tokens, [:transaction_id, :community_id, :op_name],
                name: "index_paypal_process_tokens_on_transaction", unique: true
    rescue ActiveRecord::StatementInvalid => e
      # Ignore if index already exists
      raise e unless e.message.include?("already exists")
    end
  end
  def down
    drop_table :transaction_process_tokens
  end
end
