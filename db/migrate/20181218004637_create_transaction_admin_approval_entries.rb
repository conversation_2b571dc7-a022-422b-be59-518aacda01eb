class CreateTransactionAdminApprovalEntries < ActiveRecord::Migration[5.1]
  def change
    create_table(:transaction_admin_approval_entries) do |t|
      t.integer :transaction_admin_approval_id, null: false
      t.integer :transaction_id, null: false
      t.index :transaction_admin_approval_id, name: 'index_txn_admin_approvals_txns_on_txn_admin_approvals'
      t.index :transaction_id
    end
  end
end
