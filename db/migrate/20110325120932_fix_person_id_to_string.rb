class FixPersonIdToString < ActiveRecord::Migration[7.2]
  def self.up
  	        remove_column :locations, :person_id if column_exists?(:locations, :person_id)
    remove_column :locations, :person_id if column_exists?(:locations, :person_id)
     if column_exists?(:locations, :string)
  end
  def self.down
    remove_column :locations, :person_id if column_exists?(:locations, :person_id)
    remove_column :locations, :person_id if column_exists?(:locations, :person_id)
     if column_exists?(:locations, :integer)
  end
end
