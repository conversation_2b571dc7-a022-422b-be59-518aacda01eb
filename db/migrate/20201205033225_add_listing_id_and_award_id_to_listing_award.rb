class AddListingIdAndAwardIdToListingAward < ActiveRecord::Migration[5.1]
  def change
    add_reference(:listing_awards, :listing, type: :int, limit: 11)
    add_foreign_key :listing_awards, :listings
    add_reference(:listing_awards, :award)
    add_foreign_key :listing_awards, :awards
    add_column :listing_awards, :created_at, :datetime, null: false
    add_column :listing_awards, :updated_at, :datetime, null: false
  end
end
