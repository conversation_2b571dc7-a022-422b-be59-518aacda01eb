class AddUuidColumnToListings < ActiveRecord::Migration[7.2]
  def up
    # PostgreSQL uses BYTEA for binary data instead of MySQL's BINARY type
    # PostgreSQL doesn't support AFTER clause, so column will be added at the end
    add_column :listings, :uuid, :binary, limit: 16
    # NOT NULL and UNIQUE constraints are coming in separate
    # migrations once the old data is migrated
  end
  def down
    remove_column :listings, :uuid if column_exists?(:listings, :uuid)
  end
end
