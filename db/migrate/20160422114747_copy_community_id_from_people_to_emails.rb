class CopyCommunityIdFromPeopleToEmails < ActiveRecord::Migration[7.2]
  def up
    exec_update(
      ["UPDATE emails",
       "SET community_id = -1",
       "FROM people p",
       "WHERE p.id = emails.person_id",
       "AND p.community_id = -1"].join(" "),
      "Set community_id -1",
      []
    )
  end
  def down
    exec_update(
      ["UPDATE emails",
       "SET community_id = NULL",
       "FROM people p",
       "WHERE p.id = emails.person_id",
       "AND p.community_id = -1"].join(" "),
      "Set community_id NULL",
      []
    )
  end
end
