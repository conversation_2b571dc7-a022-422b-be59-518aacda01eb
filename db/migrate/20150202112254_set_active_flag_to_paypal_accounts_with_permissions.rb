# Change the semantics of 'active' account
# Old: Account is marked as active if:
# - personal account: permissions and billing agreement are verified
# - community account: permissions are verified
# New: Account is marked as active if:
# - permissions are verified
class SetActiveFlagToPaypalAccountsWithPermissions < ActiveRecord::Migration[7.2]
  def up
    execute(<<-SQL)
      UPDATE paypal_accounts
      SET active = COALESCE(
        (SELECT order_permissions.verification_code IS NOT NULL
         FROM order_permissions
         WHERE order_permissions.paypal_account_id = paypal_accounts.id
         LIMIT 1),
        false
      )
    SQL
  end
  def down
    execute(<<-SQL)
      UPDATE paypal_accounts
      SET active = (
        SELECT CASE
          WHEN (
            ba.billing_agreement_id IS NOT NULL
            AND op.verification_code IS NOT NULL
            AND paypal_accounts.person_id IS NOT NULL
          ) OR (
            op.verification_code IS NOT NULL
            AND paypal_accounts.person_id IS NULL
          ) THEN true
          ELSE false
        END
        FROM billing_agreements ba
        FULL OUTER JOIN order_permissions op ON (ba.paypal_account_id = op.paypal_account_id)
        WHERE ba.paypal_account_id = paypal_accounts.id
           OR op.paypal_account_id = paypal_accounts.id
        LIMIT 1
      )
    SQL
  end
end
