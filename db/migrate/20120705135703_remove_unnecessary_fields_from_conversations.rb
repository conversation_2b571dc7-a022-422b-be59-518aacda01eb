class RemoveUnnecessaryFieldsFromConversations < ActiveRecord::Migration[7.2]
  def self.up
            remove_column :conversations, :reserver_name if column_exists?(:conversations, :reserver_name)
    remove_column :conversations, :pick_up_time if column_exists?(:conversations, :pick_up_time)
    remove_column :conversations, :return_time if column_exists?(:conversations, :return_time)
    remove_column :conversations, :hidden_from_owner if column_exists?(:conversations, :hidden_from_owner)
    remove_column :conversations, :hidden_from_reserver if column_exists?(:conversations, :hidden_from_reserver)
    remove_column :conversations, :favor_id if column_exists?(:conversations, :favor_id)
  end
  def self.down
    remove_column :conversations, :reserver_name if column_exists?(:conversations, :reserver_name)
     if column_exists?(:conversations, :string)
    remove_column :conversations, :pick_up_time if column_exists?(:conversations, :pick_up_time)
    remove_column :conversations, :date if column_exists?(:conversations, :date)
    remove_column :conversations, :return_time if column_exists?(:conversations, :return_time)
    remove_column :conversations, :date if column_exists?(:conversations, :date)
    remove_column :conversations, :hidden_from_owner if column_exists?(:conversations, :hidden_from_owner)
    remove_column :conversations, :hidden_from_reserver if column_exists?(:conversations, :hidden_from_reserver)
    remove_column :conversations, :favor_id if column_exists?(:conversations, :favor_id)
  end
end
