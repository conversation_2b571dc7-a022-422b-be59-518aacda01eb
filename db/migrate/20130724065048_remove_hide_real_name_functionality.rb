class RemoveHideRealNameFunctionality < ActiveRecord::Migration[7.2]
  def up
            remove_column :communities, :select_whether_name_is_shown_to_everybody if column_exists?(:communities, :select_whether_name_is_shown_to_everybody)
    remove_column :communities, :show_real_name_to_other_users if column_exists?(:communities, :show_real_name_to_other_users)
  end
  def down
    remove_column :communities, :select_whether_name_is_shown_to_everybody if column_exists?(:communities, :select_whether_name_is_shown_to_everybody)
     if column_exists?(:communities, :boolean)
    remove_column :communities, :show_real_name_to_other_users if column_exists?(:communities, :show_real_name_to_other_users)
     if column_exists?(:communities, :boolean)
  end
end
