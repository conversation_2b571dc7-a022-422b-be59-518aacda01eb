class AddMissingCommunityIdValuesToEmails < ActiveRecord::Migration[7.2]
  # The migration that duplicates emails is already adding
  # community_id to those newly created rows. This
  # migration only adds community_id to those rows that
  # were not cloned.
  def up
    execute(<<-SQL)
      UPDATE emails
      SET community_id = p.community_id
      FROM people AS p
      WHERE emails.person_id = p.id
        AND p.cloned_from IS NULL
    SQL
  end
  def down
    execute(<<-SQL)
      UPDATE emails
      SET community_id = NULL
      FROM people AS p
      WHERE emails.person_id = p.id
        AND p.cloned_from IS NULL
    SQL
  end
end
