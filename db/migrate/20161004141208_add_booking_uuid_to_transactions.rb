class AddBookingUuidToTransactions < ActiveRecord::Migration[7.2]
  def up
    # PostgreSQL uses BYTEA for binary data instead of MySQL's BINARY type
    # PostgreSQL doesn't support AFTER clause, so column will be added at the end
    add_column :transactions, :booking_uuid, :binary, limit: 16
  end
  def down
    remove_column :transactions, :booking_uuid if column_exists?(:transactions, :booking_uuid)
  end
end
