class PopulateMerchantIdToPaypalPayments < ActiveRecord::Migration[7.2]
  def up
    # To migrate seller's Sharetribe person ID to paypal payments, we have to options:
    # - A: By "transaction_id": Join the "transactions" and "listings" and get the "author_id"
    #   - Problem: There may not be listing (or transaction), if user has been deleted
    # - B: By "receiver_id" and "community_id": Join "paypal_accounts"
    #   - Problem: There may be multiple accounts per "community_id", "receiver_id" combination
    # So, what we do here is that we use both options and get the person_id's that we can.
    execute(<<-SQL)
      UPDATE paypal_payments
      SET merchant_id = (
        SELECT COALESCE(
          (SELECT listings.author_id
           FROM transactions
           LEFT JOIN listings ON transactions.listing_id = listings.id
           WHERE transactions.id = paypal_payments.transaction_id
           LIMIT 1),
          (SELECT accounts.person_id
           FROM (
             SELECT person_id, payer_id, community_id
             FROM paypal_accounts
             -- We are looking for personal account, so "person_id" must be non-null
             WHERE person_id IS NOT NULL
             -- Group by all selected columns to ensure uniqueness
             -- ONLY if there is only one hit. Ignore groups of multiple rows, since then
             -- we could not say which one is the right one
             GROUP BY person_id, payer_id, community_id
             HAVING COUNT(*) = true
           ) AS accounts
           WHERE paypal_payments.receiver_id = accounts.payer_id
             AND paypal_payments.community_id = accounts.community_id
           LIMIT 1)
        )
      )
      WHERE paypal_payments.merchant_id IS NULL
    SQL
  end
  def down
    # noop
  end
end
