class AddMissingCommunityIdValuesToPeople < ActiveRecord::Migration[7.2]
  # The migration that duplicates people is already adding
  # community_id to those newly created rows. This
  # migration only adds community_id to those rows that
  # were not cloned.
  def up
    execute(<<-SQL)
      UPDATE people
      SET community_id = cm.community_id
      FROM community_memberships AS cm
      WHERE cm.person_id = people.id
        AND people.cloned_from IS NULL
    SQL
  end
  def down
    execute("
      UPDATE people
      SET community_id = NULL
      WHERE cloned_from IS NULL
   ")
  end
end
