class RemoveMangopayFromPeople < ActiveRecord::Migration[7.2]
  def up
            remove_column :people, :mangopay_id if column_exists?(:people, :mangopay_id)
    remove_column :people, :mangopay_beneficiary_id if column_exists?(:people, :mangopay_beneficiary_id)
    remove_column :people, :bic if column_exists?(:people, :bic)
    remove_column :people, :bank_account_owner_name if column_exists?(:people, :bank_account_owner_name)
    remove_column :people, :iban if column_exists?(:people, :iban)
    remove_column :people, :bank_account_owner_address if column_exists?(:people, :bank_account_owner_address)
  end
  def down
    remove_column :people, :mangopay_id if column_exists?(:people, :mangopay_id)
     if column_exists?(:people, :string)
    remove_column :people, :bic if column_exists?(:people, :bic)
     if column_exists?(:people, :string)
    remove_column :people, :bank_account_owner_name if column_exists?(:people, :bank_account_owner_name)
     if column_exists?(:people, :string)
    remove_column :people, :iban if column_exists?(:people, :iban)
     if column_exists?(:people, :string)
    remove_column :people, :bank_account_owner_address if column_exists?(:people, :bank_account_owner_address)
     if column_exists?(:people, :string)
    remove_column :people, :mangopay_beneficiary_id if column_exists?(:people, :mangopay_beneficiary_id)
     if column_exists?(:people, :string)
  end
end
