class AddFeaturedToCustomFieldNames < ActiveRecord::Migration[5.1]
  def up
    add_column :custom_field_names, :featured, :boolean
    list_of_questions = ['house calls', 'bonded and insured', 'bbb accredited', 'certified', 'licensed', 'background checked']
    CustomFieldName.reset_column_information
    CustomFieldName.where(value: list_of_questions).update(featured: true)
  end
  def down
    remove_column :custom_field_names, :featured if column_exists?(:custom_field_names, :featured)
  end
end
