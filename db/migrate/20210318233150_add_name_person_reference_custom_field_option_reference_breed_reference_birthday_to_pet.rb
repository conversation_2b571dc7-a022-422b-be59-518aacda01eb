class AddNamePersonReferenceCustomFieldOptionReferenceBreedReferenceBirthdayToPet < ActiveRecord::Migration[5.1]
  def change
    add_reference(:pets, :parent, references: :people, type: :string, limit: 22, index: true)
    add_foreign_key :pets, :people, column: :parent_id
    add_reference(:pets, :pet_type, references: :custom_field_options, type: :int, index: true)
    add_foreign_key :pets, :custom_field_options, column: :pet_type_id
    add_reference(:pets, :breed, foreign_key: true)
    add_column :pets, :name, :string
    add_column :pets, :birthday, :datetime
    add_column :pets, :created_at, :datetime, null: false
    add_column :pets, :updated_at, :datetime, null: false
  end
end
