class RemoveOnlyPublicListingsFromCommunities < ActiveRecord::Migration[7.2]
  def up
            remove_column :communities, :only_public_listings if column_exists?(:communities, :only_public_listings)
  end
  def down
    remove_column :communities, :only_public_listings if column_exists?(:communities, :only_public_listings)
     if column_exists?(:communities, :boolean)
    remove_column :communities, default: true
  end
end
