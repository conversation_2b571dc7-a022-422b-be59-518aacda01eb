class RemoveStorefrontLabel < ActiveRecord::Migration[7.2]
  def up
            remove_column :community_customizations, :storefront_label if column_exists?(:community_customizations, :storefront_label)
  end
  def down
    remove_column :community_customizations, :storefront_label if column_exists?(:community_customizations, :storefront_label)
     if column_exists?(:community_customizations, :string)
    remove_column :community_customizations, null: true
  end
end
