class RemoveNewsItems < ActiveRecord::Migration[7.2]
  def self.up
    drop_table :news_items
            remove_column :communities, :news_enabled if column_exists?(:communities, :news_enabled)
  end
  def self.down
    remove_column :communities, :news_enabled if column_exists?(:communities, :news_enabled)
     if column_exists?(:communities, :boolean)
    create_table :news_items do |t|
      t.string :title
      t.text :content
      t.integer :community_id
      t.string :author_id
      t.timestamps
    end
  end
end
