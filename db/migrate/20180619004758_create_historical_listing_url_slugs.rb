class CreateHistoricalListingUrlSlugs < ActiveRecord::Migration[5.1]
  def change
    create_table :historical_listing_url_slugs do |t|
      t.integer :listing_id, null: false
      t.string :url_slug, null: false
      t.datetime :created_at, null: false
    end
    add_index(:historical_listing_url_slugs, :url_slug)
    add_foreign_key :historical_listing_url_slugs, :listings, on_delete: :cascade, on_update: :cascade
  end
end
