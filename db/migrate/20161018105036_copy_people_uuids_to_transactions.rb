class CopyPeopleUuidsToTransactions < ActiveRecord::Migration[7.2]
  def up
    # PostgreSQL uses different syntax for multi-table updates
    execute "UPDATE transactions SET starter_uuid = people.uuid FROM people WHERE transactions.starter_id = people.id"
    execute "UPDATE transactions SET listing_author_uuid = people.uuid FROM people WHERE transactions.listing_author_id = people.id"
  end
  def down
    execute "UPDATE transactions SET starter_uuid = NULL, listing_author_uuid = NULL"
  end
end
