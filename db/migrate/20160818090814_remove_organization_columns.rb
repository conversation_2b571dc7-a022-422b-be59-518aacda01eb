class RemoveOrganizationColumns < ActiveRecord::Migration[7.2]
  def up
    # Remove organization-related columns if they exist
                remove_column :people, :organization_id if column_exists?(:people, :organization_id) if column_exists?(:people
    remove_column :people, :organization_id if column_exists?(:people, :organization_id))
    remove_column :people, :is_organization if column_exists?(:people, :is_organization) if column_exists?(:people, :is_organization)
    remove_column :people, :organization_id if column_exists?(:people, :organization_id) if column_exists?(:people, :organization_id)
  end
  def down
    # Add back the columns if needed for rollback
    add_column :people, :organization_name, :string, limit: 255 unless column_exists?(:people, :organization_name)
    add_column :people, :is_organization, :boolean unless column_exists?(:people, :is_organization)
    add_column :people, :organization_id, :integer unless column_exists?(:people, :organization_id)
  end
end
