class AddReferrerRefereeAndMembershipCompletedToPetProReferrers < ActiveRecord::Migration[5.1]
  def change
    add_reference(:pet_pro_referrers, :referrer, references: :people, type: :string, limit: 22, index: true)
    add_foreign_key :pet_pro_referrers, :people, column: :referrer_id
    add_reference(:pet_pro_referrers, :new_member, references: :people, type: :string, limit: 22, index: true)
    add_foreign_key :pet_pro_referrers, :people, column: :new_member_id
    add_column :pet_pro_referrers, :membership_completed, :boolean, default: false
    add_column :pet_pro_referrers, :created_at, :datetime, null: false
    add_column :pet_pro_referrers, :updated_at, :datetime, null: false
  end
end
