class DeleteUnusedTables < ActiveRecord::Migration[7.2]
  def self.up
    remove_column :listings, :close_notification_sent if column_exists?(:listings, :close_notification_sent)
    drop_table :favors if table_exists?(:favors)
    drop_table :filters if table_exists?(:filters)
    drop_table :items if table_exists?(:items)
    drop_table :kassi_events if table_exists?(:kassi_events)
    drop_table :kassi_event_participations if table_exists?(:kassi_event_participations)
    drop_table :kassi_events_people if table_exists?(:kassi_events_people)
    drop_table :listing_comments if table_exists?(:listing_comments)
    drop_table :people_smerf_forms if table_exists?(:people_smerf_forms)
    drop_table :person_comments if table_exists?(:person_comments)
    drop_table :person_conversations if table_exists?(:person_conversations)
    drop_table :person_interesting_listings if table_exists?(:person_interesting_listings)
    drop_table :person_read_listings if table_exists?(:person_read_listings)
    drop_table :settings if table_exists?(:settings)
    drop_table :smerf_forms if table_exists?(:smerf_forms)
    drop_table :smerf_responses if table_exists?(:smerf_responses)
    drop_table :transactions if table_exists?(:transactions)
  end
  def self.down
    raise ActiveRecord::IrreversibleMigration, "This migration deletes whole tables which are no longer in use, so it doesn't seem useful to build the complete rollback for this."
  end
end
