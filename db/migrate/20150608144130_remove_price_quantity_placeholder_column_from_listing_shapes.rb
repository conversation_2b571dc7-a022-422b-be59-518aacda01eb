class RemovePriceQuantityPlaceholderColumnFromListingShapes < ActiveRecord::Migration[7.2]
  def up
            remove_column :listing_shapes, :price_quantity_placeholder if column_exists?(:listing_shapes, :price_quantity_placeholder)
  end
  def down
    remove_column :listing_shapes, :price_quantity_placeholder if column_exists?(:listing_shapes, :price_quantity_placeholder)
     if column_exists?(:listing_shapes, :string)
  end
end
