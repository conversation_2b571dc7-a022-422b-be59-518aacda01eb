class AddIndexes < ActiveRecord::Migration[7.2]
  def self.up
    add_index :communities_listings, [:listing_id, :community_id], :name => "index_communities_listings_on_listing_id_and_community_id" unless index_exists?(:communities_listings, [:listing_id, :community_id])
    add_index :listing_images, :listing_id unless index_exists?(:listing_images, :listing_id)
    add_index :share_types, :listing_id unless index_exists?(:share_types, :listing_id)
    add_index :listings, :listing_type if column_exists?(:listings, :listing_type) && !index_exists?(:listings, :listing_type)
    add_index :listings, :visibility if column_exists?(:listings, :visibility) && !index_exists?(:listings, :visibility)
    add_index :listings, :open if column_exists?(:listings, :open) && !index_exists?(:listings, :open)
    add_index :comments, :listing_id unless index_exists?(:comments, :listing_id)
  end
  def self.down
    remove_index :comments, :listing_id if index_exists?(:comments, :listing_id)
    remove_index :listings, :open if index_exists?(:listings, :open)
    remove_index :listings, :visibility if index_exists?(:listings, :visibility)
    remove_index :listings, :listing_type if index_exists?(:listings, :listing_type)
    remove_index :share_types, :listing_id if index_exists?(:share_types, :listing_id)
    remove_index :listing_images, :listing_id if index_exists?(:listing_images, :listing_id)
    remove_index :communities_listings, :name => :index_communities_listings_on_listing_id_and_community_id if index_exists?(:communities_listings, [:listing_id, :community_id])
  end
end
