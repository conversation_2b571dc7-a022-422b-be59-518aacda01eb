class RemoveOldPaymentColumnsFromCommunity < ActiveRecord::Migration[7.2]
  def up
    # Remove payment-related columns if they exist
                remove_column :communities, :testimonials_in_use if column_exists?(:communities, :testimonials_in_use) if column_exists?(:communities
    remove_column :communities, :testimonials_in_use if column_exists?(:communities, :testimonials_in_use))
    remove_column :communities, :vat if column_exists?(:communities, :vat) if column_exists?(:communities, :vat)
    remove_column :communities, :testimonials_in_use if column_exists?(:communities, :testimonials_in_use) if column_exists?(:communities, :testimonials_in_use)
  end
  def down
    # Add back the columns if needed for rollback
    add_column :communities, :commission_from_seller, :integer unless column_exists?(:communities, :commission_from_seller)
    add_column :communities, :vat, :integer unless column_exists?(:communities, :vat)
    add_column :communities, :testimonials_in_use, :boolean, default: true unless column_exists?(:communities, :testimonials_in_use)
  end
end
