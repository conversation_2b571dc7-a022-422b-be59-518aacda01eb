class RemoveUnnecessaryCommunityDefaults < ActiveRecord::Migration[7.2]
  def up
        remove_column :communities, :category_change_allowed if column_exists?(:communities, :category_change_allowed)
    remove_column :communities, :custom_fields_allowed if column_exists?(:communities, :custom_fields_allowed)
    remove_column :communities, :privacy_policy_change_allowed if column_exists?(:communities, :privacy_policy_change_allowed)
    remove_column :communities, :terms_change_allowed if column_exists?(:communities, :terms_change_allowed)
    change_column :communities
    remove_column :communities, :feedback_to_admin if column_exists?(:communities, :feedback_to_admin)
     if column_exists?(:communities, :boolean)
    remove_column :communities, default: true
  end
  def down
    remove_column :communities, :category_change_allowed if column_exists?(:communities, :category_change_allowed)
     if column_exists?(:communities, :boolean)
    remove_column :communities, default: false
    remove_column :communities, :custom_fields_allowed if column_exists?(:communities, :custom_fields_allowed)
     if column_exists?(:communities, :boolean)
    remove_column :communities, default: false
    remove_column :communities, :privacy_policy_change_allowed if column_exists?(:communities, :privacy_policy_change_allowed)
     if column_exists?(:communities, :boolean)
    remove_column :communities, default: false
    remove_column :communities, :terms_change_allowed if column_exists?(:communities, :terms_change_allowed)
     if column_exists?(:communities, :boolean)
    remove_column :communities, default: false
    change_column :communities
    remove_column :communities, :feedback_to_admin if column_exists?(:communities, :feedback_to_admin)
     if column_exists?(:communities, :boolean)
    remove_column :communities, default: false
  end
end
