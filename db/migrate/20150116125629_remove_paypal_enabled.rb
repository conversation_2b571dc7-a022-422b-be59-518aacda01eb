class RemovePaypalEnabled < ActiveRecord::Migration[7.2]
  def up
            remove_column :communities, :paypal_enabled if column_exists?(:communities, :paypal_enabled)
  end
  def down
    remove_column :communities, :paypal_enabled if column_exists?(:communities, :paypal_enabled)
     if column_exists?(:communities, :boolean)
    remove_column :communities, default: false
    remove_column :communities, null: false
  end
end
