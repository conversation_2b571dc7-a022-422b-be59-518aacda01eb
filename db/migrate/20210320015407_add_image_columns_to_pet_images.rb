class AddImageColumnsToPetImages < ActiveRecord::Migration[5.1]
  def change
    add_reference(:pet_images, :pet, foreign_key: true)
    add_column :pet_images, :image_file_name, :string
    add_column :pet_images, :image_content_type, :string
    add_column :pet_images, :image_file_size, :integer
    add_column :pet_images, :image_updated_at, :datetime
    add_column :pet_images, :image_processing, :boolean
    add_column :pet_images, :image_downloaded, :boolean
    add_column :pet_images, :error, :string
    add_column :pet_images, :width, :integer
    add_column :pet_images, :height, :integer
    add_column :pet_images, :author_id, :string, limit: 22
    add_column :pet_images, :position, :integer
    add_column :pet_images, :created_at, :datetime, null: false
    add_column :pet_images, :updated_at, :datetime, null: false
  end
end
