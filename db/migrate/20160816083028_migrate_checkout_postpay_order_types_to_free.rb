class MigrateCheckoutPostpayOrderTypesToFree < ActiveRecord::Migration[7.2]
  def up
    # 1) Find all listing shapes (i.e. Order Types) where transaction process is "postpay" and payment gateway is "Checkout"
    # 2) Change the listing shapes' transaction process to "none"
    execute(<<-SQL)
      UPDATE listing_shapes
      SET transaction_process_id = (
        SELECT id FROM transaction_processes
        WHERE community_id = listing_shapes.community_id
        AND process = 'none'
        AND author_is_seller = true
      )
      FROM transaction_processes txp_current, payment_gateways pg
      WHERE txp_current.id = listing_shapes.transaction_process_id
      AND pg.community_id = listing_shapes.community_id
      AND txp_current.process = 'postpay'
      AND pg.type = 'Checkout'
    SQL
  end
  def down
    # Nothing here
  end
end
