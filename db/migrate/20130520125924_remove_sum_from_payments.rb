class RemoveSumFromPayments < ActiveRecord::Migration[7.2]
  def up
            remove_column :payments, :sum_cents if column_exists?(:payments, :sum_cents) 
    remove_column :payments, :currency if column_exists?(:payments, :currency)
  end
  def down
    remove_column :payments, :sum_cents if column_exists?(:payments, :sum_cents)
     if column_exists?(:payments, :integer)
    remove_column :payments, :currency if column_exists?(:payments, :currency)
     if column_exists?(:payments, :string)
  end
end
