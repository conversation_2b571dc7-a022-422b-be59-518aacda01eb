class RemoveGatewaySpecificCommission < ActiveRecord::Migration[7.2]
  def up
        remove_column :payment_gateways, :gateway_commission_percentage if column_exists?(:payment_gateways, :gateway_commission_percentage)
    remove_column :payment_gateways, :gateway_commission_fixed_cents if column_exists?(:payment_gateways, :gateway_commission_fixed_cents)
    remove_column :payment_gateways, :gateway_commission_fixed_currency if column_exists?(:payment_gateways, :gateway_commission_fixed_currency)
  end
  def down
    remove_column :payment_gateways, :gateway_commission_percentage if column_exists?(:payment_gateways, :gateway_commission_percentage)
     if column_exists?(:payment_gateways, :integer)
    remove_column :payment_gateways, :gateway_commission_fixed_cents if column_exists?(:payment_gateways, :gateway_commission_fixed_cents)
     if column_exists?(:payment_gateways, :integer)
    remove_column :payment_gateways, :gateway_commission_fixed_currency if column_exists?(:payment_gateways, :gateway_commission_fixed_currency)
     if column_exists?(:payment_gateways, :string)
  end
end
