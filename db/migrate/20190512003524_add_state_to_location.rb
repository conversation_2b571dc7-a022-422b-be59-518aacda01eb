class AddStateToLocation < ActiveRecord::Migration[5.1]
  def up
    add_column :locations, :state_code, :string, limit: 2
    Location.all.each do |l|
      result=Geocoder.search([l.latitude, l.longitude])
      if result.first&.country == 'United States'
        l.update_column(:state_code, result.first&.state_code[0..1])
      end
      sleep 0.02 # don't exceed rate limits
    end
  end
  def down
    remove_column :locations, :state_code if column_exists?(:locations, :state_code)
  end
end
