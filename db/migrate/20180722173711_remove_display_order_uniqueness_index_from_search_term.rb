class RemoveDisplayOrderUniquenessIndexFromSearchTerm < ActiveRecord::Migration[5.1]
  def up
    remove_foreign_key :search_term_category_mappings, :categories
    remove_index :search_term_category_mappings, name: 'index_search_term_category_mappings_on_display_order'
    add_foreign_key :search_term_category_mappings, :categories, on_delete: :cascade, on_update: :cascade
  end
  def down
    # add_foreign_key :search_term_category_mappings, :categories, on_delete: :cascade, on_update: :cascade
    add_index :search_term_category_mappings, [:category_id, :display_order], unique: true, name: 'index_search_term_category_mappings_on_display_order'
  end
end
