class AddDayUnitToTxsWithBooking < ActiveRecord::Migration[7.2]
  # We have some rows in "transactions" table that do not have unit_type
  #
  def up
    name = "Add day unit type to transaction with booking and without unit type"
    exec_update([
                  "UPDATE transactions",
                  "SET unit_type = 'day'",
                  "FROM bookings",
                  "WHERE bookings.transaction_id = transactions.id",
                  "AND transactions.unit_type IS NULL"
                ].join(" "), name, [])
  end
end
