class CreateSearchTermCategoryMapping < ActiveRecord::Migration[5.1]
  def up
    create_table :search_term_category_mappings do |t|
      t.integer :category_id, null: false
      t.integer :display_order, null: false
      t.string :search_term, null: false
    end
    add_foreign_key :search_term_category_mappings, :categories, on_delete: :cascade, on_update: :cascade
    add_index :search_term_category_mappings, :search_term, unique: true
    add_index :search_term_category_mappings, [:category_id, :display_order], unique: true, name: 'index_search_term_category_mappings_on_display_order'
  end
  def down
    remove_foreign_key :search_term_category_mappings, :categories
    remove_index :search_term_category_mappings, unique: true, name: 'index_search_term_category_mappings_on_display_order'
    remove_index :search_term_category_mappings, :search_term
    drop_table :search_term_category_mappings
  end
end
