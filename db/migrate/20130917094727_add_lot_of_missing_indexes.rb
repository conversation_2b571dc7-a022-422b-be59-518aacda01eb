class AddLotOfMissingIndexes < ActiveRecord::Migration[7.2]
  def up
     add_index :community_customizations, :community_id unless index_exists?(:community_customizations, :community_id)
     add_index :categories, :parent_id unless index_exists?(:categories, :parent_id)
     add_index :share_types, :parent_id unless index_exists?(:share_types, :parent_id)
     add_index :communities_listings, :community_id unless index_exists?(:communities_listings, :community_id)
     add_index :community_memberships, :community_id unless index_exists?(:community_memberships, :community_id)
     add_index :emails, :person_id unless index_exists?(:emails, :person_id)
     add_index :event_feed_events, :community_id unless index_exists?(:event_feed_events, :community_id)
     add_index :invitations, :inviter_id unless index_exists?(:invitations, :inviter_id)
     add_index :invitations, :code unless index_exists?(:invitations, :code)
     add_index :listing_followers, :person_id unless index_exists?(:listing_followers, :person_id)
     add_index :organization_memberships, :person_id unless index_exists?(:organization_memberships, :person_id)
     add_index :payment_rows, :payment_id unless index_exists?(:payment_rows, :payment_id)
     add_index :payments, :payer_id unless index_exists?(:payments, :payer_id)
     add_index :payments, :conversation_id unless index_exists?(:payments, :conversation_id)
     add_index :people, :id unless index_exists?(:people, :id)

     # remove old style category indexes
     
     remove_index :listings, :name => "index_listings_on_category" if index_name_exists?(:listings, "index_listings_on_category")
     remove_index :listings, :name => "index_listings_on_share_type" if index_name_exists?(:listings, "index_listings_on_share_type")

     add_index :listings, :category_id unless index_exists?(:listings, :category_id)
     add_index :listings, :share_type_id unless index_exists?(:listings, :share_type_id)


   end

   def down
     remove_index :community_customizations, :community_id
     remove_index :categories, :parent_id
     remove_index :share_types, :parent_id
     remove_index :communities_listings, :community_id
     remove_index :community_memberships, :community_id
     remove_index :emails, :person_id
     remove_index :event_feed_events, :community_id
     remove_index :invitations, :inviter_id
     remove_index :invitations, :code
     remove_index :listing_followers, :person_id
     remove_index :organization_memberships, :person_id
     remove_index :payment_rows, :payment_id
     remove_index :payments, :payer_id
     remove_index :payments, :conversation_id
     remove_index :people, :id

     remove_index :listings, :category_id
     remove_index :listings, :share_type_id

     # if rolling back return the old ones
     add_index :listings, :category_old, :name => "index_listings_on_category"
     add_index :listings, :share_type_old, :name => "index_listings_on_share_type"

   end
end
