class RemoveRedirectToDomainFromCommunities < ActiveRecord::Migration[7.2]
  def up
            remove_column :communities, :redirect_to_domain if column_exists?(:communities, :redirect_to_domain)
  end
  def down
    remove_column :communities, :redirect_to_domain if column_exists?(:communities, :redirect_to_domain)
     if column_exists?(:communities, :boolean)
    remove_column :communities, default: false
    remove_column :communities, null: false
  end
end
