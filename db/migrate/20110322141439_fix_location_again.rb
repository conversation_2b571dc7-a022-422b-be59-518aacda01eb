class FixLocationAgain < ActiveRecord::Migration[7.2]
  def self.up
  	        remove_column :listings, :destionation_loc_id if column_exists?(:listings, :destionation_loc_id)
    remove_column :listings, :destination_loc_id if column_exists?(:listings, :destination_loc_id)
     if column_exists?(:listings, :integer)
  end
  def self.down
    remove_column :listings, :destionation_loc_id if column_exists?(:listings, :destionation_loc_id)
    remove_column :listings, :destination_loc_id if column_exists?(:listings, :destination_loc_id)
     if column_exists?(:listings, :integer)
  end
end
