class RenameTypeToListingType < ActiveRecord::Migration[7.2]
  def self.up
            remove_column :listings, :type if column_exists?(:listings, :type)
    remove_column :listings, :listings_type if column_exists?(:listings, :listings_type)
     if column_exists?(:listings, :string)
  end
  def self.down
    remove_column :listings, :listings_type if column_exists?(:listings, :listings_type)
    remove_column :listings, :type if column_exists?(:listings, :type)
     if column_exists?(:listings, :string)
  end
end
