class ConvertTextColumnsToUtf8mb4 < ActiveRecord::Migration[5.1] 
  def up
    execute "ALTER TABLE messages MODIFY content TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"
    execute "ALTER TABLE messages MODIFY summary TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"
    execute "ALTER TABLE auto_response_messages MODIFY message_text TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"
  end

  def down
    execute "ALTER TABLE messages MODIFY content TEXT CHARACTER SET utf8 COLLATE utf8_general_ci"
    execute "ALTER TABLE messages MODIFY summary TEXT CHARACTER SET utf8 COLLATE utf8_general_ci"
    execute "ALTER TABLE auto_response_messages MODIFY message_text TEXT CHARACTER SET utf8 COLLATE utf8_general_ci"
  end
end
